<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Helpers\SecurityHelper;

class SecurityEnhancement
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check for suspicious activity
        if (SecurityHelper::detectSuspiciousActivity()) {
            return response()->json([
                'error' => 'Suspicious activity detected',
                'message' => 'Your request has been blocked for security reasons.'
            ], 403);
        }

        // Validate session security
        if (auth()->check() && !SecurityHelper::validateSession()) {
            auth()->logout();
            return redirect()->route('showloginform')
                ->with('error', 'Your session has expired for security reasons.');
        }

        // Sanitize input data
        $this->sanitizeRequestData($request);

        $response = $next($request);

        // Add security headers
        $this->addSecurityHeaders($response);

        return $response;
    }

    /**
     * Sanitize request data
     *
     * @param Request $request
     * @return void
     */
    private function sanitizeRequestData(Request $request): void
    {
        if ($request->isMethod('POST') || $request->isMethod('PUT') || $request->isMethod('PATCH')) {
            $sanitizationRules = [
                'email' => 'email',
                'name' => 'alpha',
                'category' => 'alphanumeric',
                'slug' => 'slug',
                'phone' => 'alphanumeric',
                'url' => 'url',
            ];

            $sanitizedData = SecurityHelper::sanitizeInput($request->all(), $sanitizationRules);
            $request->merge($sanitizedData);
        }
    }

    /**
     * Add security headers to response
     *
     * @param Response $response
     * @return void
     */
    private function addSecurityHeaders(Response $response): void
    {
        // Content Security Policy
        $response->headers->set('Content-Security-Policy', SecurityHelper::generateCSPHeader());
        
        // X-Frame-Options
        $response->headers->set('X-Frame-Options', 'DENY');
        
        // X-Content-Type-Options
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        
        // X-XSS-Protection
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        
        // Referrer Policy
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        
        // Permissions Policy
        $response->headers->set('Permissions-Policy', 
            'geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=()');
        
        // Strict Transport Security (HTTPS only)
        if ($this->isHttps()) {
            $response->headers->set('Strict-Transport-Security', 
                'max-age=31536000; includeSubDomains; preload');
        }
        
        // Remove server information
        $response->headers->remove('Server');
        $response->headers->remove('X-Powered-By');
        
        // Add custom security headers
        $response->headers->set('X-Security-Enhanced', 'true');
        $response->headers->set('X-Request-ID', $this->generateRequestId());
    }

    /**
     * Check if request is HTTPS
     *
     * @return bool
     */
    private function isHttps(): bool
    {
        return request()->secure() || 
               request()->header('X-Forwarded-Proto') === 'https' ||
               request()->header('X-Forwarded-SSL') === 'on';
    }

    /**
     * Generate unique request ID for tracking
     *
     * @return string
     */
    private function generateRequestId(): string
    {
        return uniqid('req_', true);
    }
}
