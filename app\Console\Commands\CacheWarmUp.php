<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Helpers\CacheHelper;

class CacheWarmUp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:warm-up {--force : Force cache warm-up even in development}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Warm up application cache for better performance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!app()->environment('production') && !$this->option('force')) {
            $this->warn('Cache warm-up is typically only needed in production.');
            if (!$this->confirm('Do you want to continue?')) {
                return 0;
            }
        }

        $this->info('Starting cache warm-up...');
        
        $bar = $this->output->createProgressBar(5);
        $bar->start();

        try {
            $results = CacheHelper::warmUp();
            
            $bar->advance();
            $this->newLine();
            
            foreach ($results as $type => $result) {
                if ($type === 'error') {
                    $this->error("Error: {$result}");
                } else {
                    $status = $result ? '✓' : '✗';
                    $this->line("{$status} {$type}: " . ($result ? 'Success' : 'Failed'));
                }
                $bar->advance();
            }
            
            $bar->finish();
            $this->newLine(2);
            $this->info('Cache warm-up completed successfully!');
            
            // Show cache statistics
            $this->showCacheStats();
            
            return 0;
            
        } catch (\Exception $e) {
            $bar->finish();
            $this->newLine();
            $this->error('Cache warm-up failed: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Show cache statistics
     */
    private function showCacheStats()
    {
        $this->info('Cache Statistics:');
        $stats = CacheHelper::getStats();
        
        $this->table(
            ['Metric', 'Value'],
            [
                ['Driver', $stats['driver']],
                ['Enabled', $stats['cache_enabled'] ? 'Yes' : 'No'],
                ['Size', $stats['cache_size'] ?? 'Unknown'],
                ['Keys', $stats['cache_keys'] ?? 'Unknown'],
                ['Hit Ratio', isset($stats['hit_ratio']) ? $stats['hit_ratio'] . '%' : 'Unknown'],
            ]
        );
    }
}
