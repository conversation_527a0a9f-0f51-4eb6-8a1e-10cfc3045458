<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'sku',
        'barcode',
        'category_id',
        'brand_id',
        'supplier_id',
        'amount',
        'compare_price',
        'cost_price',
        'stock_quantity',
        'min_stock_level',
        'track_inventory',
        'stock_status',
        'weight',
        'length',
        'width',
        'height',
        'status',
        'is_featured',
        'is_digital',
        'requires_shipping',
        'is_taxable',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_title',
        'og_description',
        'og_image',
        'featured_image',
        'gallery_images',
        'has_variants',
        'variant_options',
        'tags',
        'care_instructions',
        'warranty_info',
        'available_from',
        'available_until',
        'view_count',
        'purchase_count',
        'average_rating',
        'review_count',
        'published_at',
    ];

    protected $casts = [
        'gallery_images' => 'array',
        'variant_options' => 'array',
        'tags' => 'array',
        'amount' => 'decimal:2',
        'compare_price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'length' => 'decimal:2',
        'width' => 'decimal:2',
        'height' => 'decimal:2',
        'track_inventory' => 'boolean',
        'is_featured' => 'boolean',
        'is_digital' => 'boolean',
        'requires_shipping' => 'boolean',
        'is_taxable' => 'boolean',
        'has_variants' => 'boolean',
        'available_from' => 'date',
        'available_until' => 'date',
        'published_at' => 'datetime',
        'average_rating' => 'decimal:2',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
            if (empty($product->sku)) {
                $product->sku = 'PRD-' . strtoupper(Str::random(8));
            }
        });

        static::updating(function ($product) {
            if ($product->isDirty('name') && empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });
    }

    /**
     * Get the category that owns the product
     */
    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    /**
     * Get the first image URL
     */
    public function getFirstImageAttribute()
    {
        if ($this->images && is_array($this->images) && count($this->images) > 0) {
            return $this->images[0];
        }
        return null;
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute()
    {
        return '$' . number_format($this->amount, 2);
    }

    /**
     * Get the first gallery image URL
     */
    public function getFirstGalleryImageAttribute()
    {
        if ($this->gallery_images && is_array($this->gallery_images) && count($this->gallery_images) > 0) {
            return $this->gallery_images[0];
        }
        return $this->featured_image;
    }

    /**
     * Scope for active products
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for featured products
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for products with stock
     */
    public function scopeInStock($query)
    {
        return $query->where('stock_quantity', '>', 0);
    }
}
