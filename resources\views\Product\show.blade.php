@extends('Layouts.app')
@section('title', 'Product Details - ' . $product->name)
@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">{{ $product->name }}</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin-dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('products.index') }}">Products</a></li>
                    <li class="breadcrumb-item active">{{ $product->name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('products.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Products
            </a>
            <a href="{{ route('products.edit', $product->id) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit Product
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Product Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Product Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Name:</td>
                                    <td>{{ $product->name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">SKU:</td>
                                    <td><span class="badge badge-light-info">{{ $product->sku }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Slug:</td>
                                    <td><code>{{ $product->slug }}</code></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Category:</td>
                                    <td>
                                        @if($product->category)
                                            <span class="badge badge-light-success">{{ $product->category->category }}</span>
                                        @else
                                            <span class="text-muted">No Category</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Status:</td>
                                    <td>{!! Helper::getStatusBadge($product->status === 'active' ? 1 : 0) !!}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Selling Amount:</td>
                                    <td class="text-success fw-bold">${{ number_format($product->amount, 2) }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Cost Price:</td>
                                    <td>
                                        @if($product->cost_price)
                                            ${{ number_format($product->cost_price, 2) }}
                                        @else
                                            <span class="text-muted">Not set</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Stock Quantity:</td>
                                    <td>
                                        @if($product->stock_quantity > 10)
                                            <span class="badge badge-success">{{ $product->stock_quantity }} in stock</span>
                                        @elseif($product->stock_quantity > 0)
                                            <span class="badge badge-warning">{{ $product->stock_quantity }} low stock</span>
                                        @else
                                            <span class="badge badge-danger">Out of stock</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Weight:</td>
                                    <td>
                                        @if($product->weight)
                                            {{ $product->weight }} kg
                                        @else
                                            <span class="text-muted">Not set</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Dimensions:</td>
                                    <td>
                                        @if($product->length || $product->width || $product->height)
                                            {{ $product->length }}L x {{ $product->width }}W x {{ $product->height }}H
                                        @else
                                            <span class="text-muted">Not set</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($product->short_description)
                    <div class="mt-3">
                        <h6 class="fw-bold">Short Description:</h6>
                        <p class="text-muted">{{ $product->short_description }}</p>
                    </div>
                    @endif

                    @if($product->description)
                    <div class="mt-3">
                        <h6 class="fw-bold">Description:</h6>
                        <div class="text-muted">
                            {!! nl2br(e($product->description)) !!}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Product Stats & Options -->
        <div class="col-lg-4">
            <!-- Quick Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Stats</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Featured Product:</span>
                        @if($product->is_featured)
                            <span class="badge badge-warning">Yes</span>
                        @else
                            <span class="badge badge-light">No</span>
                        @endif
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Track Inventory:</span>
                        @if($product->track_inventory)
                            <span class="badge badge-success">Enabled</span>
                        @else
                            <span class="badge badge-secondary">Disabled</span>
                        @endif
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Created:</span>
                        <span class="text-muted">{{ $product->created_at->format('M d, Y') }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Last Updated:</span>
                        <span class="text-muted">{{ $product->updated_at->format('M d, Y') }}</span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('products.edit', $product->id) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Product
                        </a>
                        <button type="button" class="btn btn-danger delete-product" data-id="{{ $product->id }}">
                            <i class="fas fa-trash"></i> Delete Product
                        </button>
                        <hr>
                        <a href="{{ route('products.index') }}" class="btn btn-secondary">
                            <i class="fas fa-list"></i> All Products
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Delete product
    $('.delete-product').on('click', function() {
        var id = $(this).data('id');

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                showLoading('Deleting product...');
                $.ajax({
                    url: '/products/' + id,
                    type: 'DELETE',
                    headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                    success: function(res) {
                        hideLoading();
                        if (res.success) {
                            showSuccess('Success', res.message);
                            setTimeout(function() {
                                window.location.href = '{{ route("products.index") }}';
                            }, 1500);
                        } else {
                            showError('Error', res.message || 'Failed to delete product');
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        showError('Error', xhr.responseJSON?.message || 'Failed to delete product');
                    }
                });
            }
        });
    });
});
</script>
@endsection
