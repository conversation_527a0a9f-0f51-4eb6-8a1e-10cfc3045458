<?php

namespace Tests\Feature;

use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class ProductControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test category
        ProductCategory::factory()->create([
            'id' => 1,
            'category' => 'Test Category',
            'status' => 'active'
        ]);
    }

    /** @test */
    public function it_can_store_a_new_product()
    {
        $productData = [
            'name' => 'Test Product',
            'description' => 'This is a test product',
            'short_description' => 'Short description',
            'amount' => 99.99,
            'category_id' => 1,
            'status' => 'active',
            'stock_quantity' => 10,
            'cost_price' => 50.00,
            'track_inventory' => true,
            'is_featured' => false,
        ];

        $response = $this->postJson('/products', $productData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Product created successfully'
                ]);

        $this->assertDatabaseHas('products', [
            'name' => 'Test Product',
            'amount' => 99.99,
            'status' => 'active',
            'stock_quantity' => 10
        ]);
    }

    /** @test */
    public function it_generates_slug_automatically_if_not_provided()
    {
        $productData = [
            'name' => 'Test Product With Auto Slug',
            'amount' => 99.99,
            'status' => 'active',
            'stock_quantity' => 10,
        ];

        $response = $this->postJson('/products', $productData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('products', [
            'name' => 'Test Product With Auto Slug',
            'slug' => 'test-product-with-auto-slug'
        ]);
    }

    /** @test */
    public function it_generates_sku_automatically_if_not_provided()
    {
        $productData = [
            'name' => 'Test Product With Auto SKU',
            'amount' => 99.99,
            'status' => 'active',
            'stock_quantity' => 10,
        ];

        $response = $this->postJson('/products', $productData);

        $response->assertStatus(200);

        $product = Product::where('name', 'Test Product With Auto SKU')->first();
        $this->assertNotNull($product);
        $this->assertStringStartsWith('PRD-', $product->sku);
        $this->assertEquals(12, strlen($product->sku)); // PRD- + 8 characters
    }

    /** @test */
    public function it_validates_required_fields()
    {
        $response = $this->postJson('/products', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'amount', 'status', 'stock_quantity']);
    }

    /** @test */
    public function it_handles_cache_failures_gracefully()
    {
        // Mock cache to throw exception
        Cache::shouldReceive('forget')
             ->andThrow(new \Exception('Cache service unavailable'));

        $productData = [
            'name' => 'Test Product Cache Failure',
            'amount' => 99.99,
            'status' => 'active',
            'stock_quantity' => 10,
        ];

        $response = $this->postJson('/products', $productData);

        // Should still succeed even if cache fails
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Product created successfully'
                ]);

        $this->assertDatabaseHas('products', [
            'name' => 'Test Product Cache Failure'
        ]);
    }

    /** @test */
    public function it_ensures_unique_slug()
    {
        // Create first product
        Product::create([
            'name' => 'Duplicate Name',
            'slug' => 'duplicate-name',
            'amount' => 99.99,
            'status' => 'active',
            'stock_quantity' => 10,
        ]);

        // Try to create second product with same name
        $productData = [
            'name' => 'Duplicate Name',
            'amount' => 89.99,
            'status' => 'active',
            'stock_quantity' => 5,
        ];

        $response = $this->postJson('/products', $productData);

        $response->assertStatus(200);

        // Check that second product has different slug
        $products = Product::where('name', 'Duplicate Name')->get();
        $this->assertCount(2, $products);
        
        $slugs = $products->pluck('slug')->toArray();
        $this->assertContains('duplicate-name', $slugs);
        $this->assertContains('duplicate-name-1', $slugs);
    }

    /** @test */
    public function it_ensures_unique_sku()
    {
        // Create first product with specific SKU
        Product::create([
            'name' => 'First Product',
            'sku' => 'TEST-SKU-001',
            'amount' => 99.99,
            'status' => 'active',
            'stock_quantity' => 10,
        ]);

        // Try to create second product with same SKU
        $productData = [
            'name' => 'Second Product',
            'sku' => 'TEST-SKU-001',
            'amount' => 89.99,
            'status' => 'active',
            'stock_quantity' => 5,
        ];

        $response = $this->postJson('/products', $productData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['sku']);
    }
}
