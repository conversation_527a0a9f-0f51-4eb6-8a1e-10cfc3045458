<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class NotificationHelper
{
    /**
     * Notification types
     */
    const TYPES = [
        'success' => 'success',
        'error' => 'danger',
        'warning' => 'warning',
        'info' => 'info',
        'primary' => 'primary',
        'secondary' => 'secondary',
    ];

    /**
     * Add a notification to the session
     *
     * @param string $type
     * @param string $message
     * @param string $title
     * @param array $options
     * @return void
     */
    public static function add(string $type, string $message, string $title = '', array $options = []): void
    {
        $notification = [
            'type' => self::TYPES[$type] ?? 'info',
            'message' => $message,
            'title' => $title,
            'dismissible' => $options['dismissible'] ?? true,
            'timeout' => $options['timeout'] ?? 5000,
            'icon' => $options['icon'] ?? self::getDefaultIcon($type),
            'position' => $options['position'] ?? 'top-right',
            'id' => uniqid('notification_'),
            'timestamp' => now()->toISOString(),
        ];

        $notifications = Session::get('notifications', []);
        $notifications[] = $notification;
        Session::put('notifications', $notifications);
    }

    /**
     * Add success notification
     *
     * @param string $message
     * @param string $title
     * @param array $options
     * @return void
     */
    public static function success(string $message, string $title = 'Success!', array $options = []): void
    {
        self::add('success', $message, $title, $options);
    }

    /**
     * Add error notification
     *
     * @param string $message
     * @param string $title
     * @param array $options
     * @return void
     */
    public static function error(string $message, string $title = 'Error!', array $options = []): void
    {
        self::add('error', $message, $title, $options);
    }

    /**
     * Add warning notification
     *
     * @param string $message
     * @param string $title
     * @param array $options
     * @return void
     */
    public static function warning(string $message, string $title = 'Warning!', array $options = []): void
    {
        self::add('warning', $message, $title, $options);
    }

    /**
     * Add info notification
     *
     * @param string $message
     * @param string $title
     * @param array $options
     * @return void
     */
    public static function info(string $message, string $title = 'Info', array $options = []): void
    {
        self::add('info', $message, $title, $options);
    }

    /**
     * Get all notifications and clear them from session
     *
     * @return array
     */
    public static function getAndClear(): array
    {
        $notifications = Session::get('notifications', []);
        Session::forget('notifications');
        return $notifications;
    }

    /**
     * Get all notifications without clearing
     *
     * @return array
     */
    public static function get(): array
    {
        return Session::get('notifications', []);
    }

    /**
     * Clear all notifications
     *
     * @return void
     */
    public static function clear(): void
    {
        Session::forget('notifications');
    }

    /**
     * Get default icon for notification type
     *
     * @param string $type
     * @return string
     */
    private static function getDefaultIcon(string $type): string
    {
        $icons = [
            'success' => 'fas fa-check-circle',
            'error' => 'fas fa-exclamation-circle',
            'warning' => 'fas fa-exclamation-triangle',
            'info' => 'fas fa-info-circle',
            'primary' => 'fas fa-bell',
            'secondary' => 'fas fa-bell',
        ];

        return $icons[$type] ?? 'fas fa-bell';
    }

    /**
     * Add notification from exception
     *
     * @param \Exception $exception
     * @param string $userMessage
     * @return void
     */
    public static function fromException(\Exception $exception, string $userMessage = 'An error occurred'): void
    {
        // Log the actual exception
        Log::error('Exception occurred: ' . $exception->getMessage(), [
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ]);

        // Show user-friendly message
        self::error($userMessage, 'Error');
    }

    /**
     * Add bulk notifications
     *
     * @param array $notifications
     * @return void
     */
    public static function addBulk(array $notifications): void
    {
        foreach ($notifications as $notification) {
            self::add(
                $notification['type'] ?? 'info',
                $notification['message'] ?? '',
                $notification['title'] ?? '',
                $notification['options'] ?? []
            );
        }
    }

    /**
     * Create notification for CRUD operations
     *
     * @param string $operation
     * @param string $resource
     * @param bool $success
     * @param string $customMessage
     * @return void
     */
    public static function crud(string $operation, string $resource, bool $success = true, string $customMessage = ''): void
    {
        $operations = [
            'create' => ['created', 'create'],
            'update' => ['updated', 'update'],
            'delete' => ['deleted', 'delete'],
            'restore' => ['restored', 'restore'],
        ];

        if (!isset($operations[$operation])) {
            return;
        }

        [$pastTense, $presentTense] = $operations[$operation];
        
        if ($success) {
            $message = $customMessage ?: ucfirst($resource) . " has been {$pastTense} successfully.";
            self::success($message, 'Success!');
        } else {
            $message = $customMessage ?: "Failed to {$presentTense} {$resource}. Please try again.";
            self::error($message, 'Error!');
        }
    }

    /**
     * Create notification with progress bar
     *
     * @param string $message
     * @param int $progress
     * @param string $title
     * @return void
     */
    public static function progress(string $message, int $progress = 0, string $title = 'Processing...'): void
    {
        self::add('info', $message, $title, [
            'progress' => max(0, min(100, $progress)),
            'timeout' => 0, // Don't auto-dismiss progress notifications
            'dismissible' => false,
        ]);
    }

    /**
     * Create notification with action buttons
     *
     * @param string $type
     * @param string $message
     * @param array $actions
     * @param string $title
     * @return void
     */
    public static function withActions(string $type, string $message, array $actions, string $title = ''): void
    {
        self::add($type, $message, $title, [
            'actions' => $actions,
            'timeout' => 0, // Don't auto-dismiss notifications with actions
        ]);
    }

    /**
     * Create persistent notification (doesn't auto-dismiss)
     *
     * @param string $type
     * @param string $message
     * @param string $title
     * @return void
     */
    public static function persistent(string $type, string $message, string $title = ''): void
    {
        self::add($type, $message, $title, [
            'timeout' => 0,
            'dismissible' => true,
        ]);
    }

    /**
     * Create notification for validation errors
     *
     * @param array $errors
     * @param string $title
     * @return void
     */
    public static function validationErrors(array $errors, string $title = 'Validation Error'): void
    {
        $message = 'Please correct the following errors:';
        $errorList = [];
        
        foreach ($errors as $field => $fieldErrors) {
            if (is_array($fieldErrors)) {
                $errorList = array_merge($errorList, $fieldErrors);
            } else {
                $errorList[] = $fieldErrors;
            }
        }

        self::add('error', $message, $title, [
            'errors' => $errorList,
            'timeout' => 0,
        ]);
    }

    /**
     * Create system notification
     *
     * @param string $message
     * @param string $level
     * @return void
     */
    public static function system(string $message, string $level = 'info'): void
    {
        self::add($level, $message, 'System Notification', [
            'icon' => 'fas fa-cog',
            'timeout' => 8000,
        ]);
    }

    /**
     * Get notification count
     *
     * @return int
     */
    public static function count(): int
    {
        return count(Session::get('notifications', []));
    }

    /**
     * Check if there are any notifications
     *
     * @return bool
     */
    public static function hasNotifications(): bool
    {
        return self::count() > 0;
    }

    /**
     * Get notifications by type
     *
     * @param string $type
     * @return array
     */
    public static function getByType(string $type): array
    {
        $notifications = self::get();
        return array_filter($notifications, function ($notification) use ($type) {
            return $notification['type'] === (self::TYPES[$type] ?? $type);
        });
    }

    /**
     * Convert Laravel flash messages to notifications
     *
     * @return void
     */
    public static function convertFlashMessages(): void
    {
        $flashTypes = ['success', 'error', 'warning', 'info'];
        
        foreach ($flashTypes as $type) {
            if (Session::has($type)) {
                $message = Session::get($type);
                if (is_string($message)) {
                    self::add($type, $message);
                    Session::forget($type);
                }
            }
        }
    }
}
