<?php
use App\Helpers\NotificationHelper;
$notifications = NotificationHelper::getAndClear();
?>

<?php if(count($notifications) > 0): ?>
<div id="notification-container" class="position-fixed" style="top: 20px; right: 20px; z-index: 9999; max-width: 400px;">
    <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="alert alert-<?php echo e($notification['type']); ?> alert-dismissible fade show notification-item mb-3 shadow-sm" 
             role="alert" 
             id="<?php echo e($notification['id']); ?>"
             data-timeout="<?php echo e($notification['timeout']); ?>"
             style="border-radius: 0.5rem; border: none; animation: slideInRight 0.3s ease-out;">
            
            <div class="d-flex align-items-start">
                <?php if($notification['icon']): ?>
                    <div class="me-3 mt-1">
                        <i class="<?php echo e($notification['icon']); ?> fs-5"></i>
                    </div>
                <?php endif; ?>
                
                <div class="flex-grow-1">
                    <?php if($notification['title']): ?>
                        <h6 class="alert-heading mb-1 fw-bold"><?php echo e($notification['title']); ?></h6>
                    <?php endif; ?>
                    
                    <div class="notification-message">
                        <?php echo e($notification['message']); ?>

                    </div>
                    
                    <?php if(isset($notification['errors']) && is_array($notification['errors'])): ?>
                        <ul class="mb-0 mt-2 small">
                            <?php $__currentLoopData = $notification['errors']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    <?php endif; ?>
                    
                    <?php if(isset($notification['progress'])): ?>
                        <div class="progress mt-2" style="height: 6px;">
                            <div class="progress-bar" 
                                 role="progressbar" 
                                 style="width: <?php echo e($notification['progress']); ?>%"
                                 aria-valuenow="<?php echo e($notification['progress']); ?>" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                            </div>
                        </div>
                        <small class="text-muted"><?php echo e($notification['progress']); ?>% complete</small>
                    <?php endif; ?>
                    
                    <?php if(isset($notification['actions']) && is_array($notification['actions'])): ?>
                        <div class="mt-2">
                            <?php $__currentLoopData = $notification['actions']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $action): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <button type="button" 
                                        class="btn btn-sm <?php echo e($action['class'] ?? 'btn-outline-primary'); ?> me-2"
                                        onclick="<?php echo e($action['onclick'] ?? ''); ?>"
                                        <?php if(isset($action['url'])): ?> onclick="window.location.href='<?php echo e($action['url']); ?>'" <?php endif; ?>>
                                    <?php if(isset($action['icon'])): ?>
                                        <i class="<?php echo e($action['icon']); ?> me-1"></i>
                                    <?php endif; ?>
                                    <?php echo e($action['title']); ?>

                                </button>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if($notification['dismissible']): ?>
                    <button type="button" 
                            class="btn-close" 
                            data-bs-dismiss="alert" 
                            aria-label="Close"
                            style="font-size: 0.75rem;">
                    </button>
                <?php endif; ?>
            </div>
            
            <div class="notification-timestamp small text-muted mt-2">
                <i class="fas fa-clock me-1"></i>
                <?php echo e(\Carbon\Carbon::parse($notification['timestamp'])->diffForHumans()); ?>

            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<style>
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification-item {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95) !important;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.notification-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.alert-success.notification-item {
    border-left-color: #198754;
    background-color: rgba(25, 135, 84, 0.1) !important;
}

.alert-danger.notification-item {
    border-left-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1) !important;
}

.alert-warning.notification-item {
    border-left-color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1) !important;
}

.alert-info.notification-item {
    border-left-color: #0dcaf0;
    background-color: rgba(13, 202, 240, 0.1) !important;
}

.alert-primary.notification-item {
    border-left-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1) !important;
}

.notification-item .btn-close {
    opacity: 0.5;
    transition: opacity 0.2s ease;
}

.notification-item .btn-close:hover {
    opacity: 1;
}

.notification-item .progress {
    border-radius: 3px;
    background-color: rgba(0,0,0,0.1);
}

.notification-item .progress-bar {
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    #notification-container {
        left: 10px;
        right: 10px;
        max-width: none;
    }
    
    .notification-item {
        font-size: 0.9rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .notification-item {
        background-color: rgba(33, 37, 41, 0.95) !important;
        color: #fff;
    }
    
    .notification-timestamp {
        color: #adb5bd !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-dismiss notifications with timeout
    document.querySelectorAll('.notification-item[data-timeout]').forEach(function(notification) {
        const timeout = parseInt(notification.dataset.timeout);
        
        if (timeout > 0) {
            setTimeout(function() {
                dismissNotification(notification);
            }, timeout);
        }
    });
    
    // Handle manual dismissal
    document.querySelectorAll('.notification-item .btn-close').forEach(function(closeBtn) {
        closeBtn.addEventListener('click', function() {
            const notification = this.closest('.notification-item');
            dismissNotification(notification);
        });
    });
    
    function dismissNotification(notification) {
        notification.style.animation = 'slideOutRight 0.3s ease-out';
        
        setTimeout(function() {
            notification.remove();
            
            // Remove container if no notifications left
            const container = document.getElementById('notification-container');
            if (container && container.children.length === 0) {
                container.remove();
            }
        }, 300);
    }
    
    // Add click-to-dismiss functionality
    document.querySelectorAll('.notification-item').forEach(function(notification) {
        notification.addEventListener('click', function(e) {
            // Don't dismiss if clicking on buttons or close button
            if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
                return;
            }
            
            // Only dismiss if notification is dismissible
            if (this.querySelector('.btn-close')) {
                dismissNotification(this);
            }
        });
    });
});

// Global function to add notifications via JavaScript
window.addNotification = function(type, message, title = '', options = {}) {
    const container = document.getElementById('notification-container') || createNotificationContainer();
    
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show notification-item mb-3 shadow-sm`;
    notification.style.cssText = 'border-radius: 0.5rem; border: none; animation: slideInRight 0.3s ease-out;';
    notification.id = 'notification_' + Date.now();
    
    const icon = options.icon || getDefaultIcon(type);
    const timeout = options.timeout || 5000;
    
    notification.innerHTML = `
        <div class="d-flex align-items-start">
            ${icon ? `<div class="me-3 mt-1"><i class="${icon} fs-5"></i></div>` : ''}
            <div class="flex-grow-1">
                ${title ? `<h6 class="alert-heading mb-1 fw-bold">${title}</h6>` : ''}
                <div class="notification-message">${message}</div>
            </div>
            <button type="button" class="btn-close" aria-label="Close" style="font-size: 0.75rem;"></button>
        </div>
        <div class="notification-timestamp small text-muted mt-2">
            <i class="fas fa-clock me-1"></i>Just now
        </div>
    `;
    
    container.appendChild(notification);
    
    // Auto-dismiss
    if (timeout > 0) {
        setTimeout(() => dismissNotification(notification), timeout);
    }
    
    // Add event listeners
    notification.querySelector('.btn-close').addEventListener('click', () => dismissNotification(notification));
};

function createNotificationContainer() {
    const container = document.createElement('div');
    container.id = 'notification-container';
    container.className = 'position-fixed';
    container.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    document.body.appendChild(container);
    return container;
}

function getDefaultIcon(type) {
    const icons = {
        'success': 'fas fa-check-circle',
        'danger': 'fas fa-exclamation-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle',
        'primary': 'fas fa-bell'
    };
    return icons[type] || 'fas fa-bell';
}

function dismissNotification(notification) {
    notification.style.animation = 'slideOutRight 0.3s ease-out';
    setTimeout(() => {
        notification.remove();
        const container = document.getElementById('notification-container');
        if (container && container.children.length === 0) {
            container.remove();
        }
    }, 300);
}
</script>
<?php endif; ?>
<?php /**PATH D:\xamp8.2\htdocs\abhishek_work\arclok_admin\resources\views/Components/notifications.blade.php ENDPATH**/ ?>