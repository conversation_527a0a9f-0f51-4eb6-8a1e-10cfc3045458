/**
 * Lazy Loading Implementation for Images and Components
 * Optimizes page load performance by loading content only when needed
 */

class LazyLoader {
    constructor(options = {}) {
        this.options = {
            imageSelector: 'img[data-src]',
            componentSelector: '[data-lazy-component]',
            rootMargin: '50px',
            threshold: 0.1,
            enablePlaceholder: true,
            fadeInDuration: 300,
            ...options
        };

        this.imageObserver = null;
        this.componentObserver = null;
        this.loadedImages = new Set();
        this.loadedComponents = new Set();

        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            this.setupImageObserver();
            this.setupComponentObserver();
            this.observeElements();
        } else {
            // Fallback for older browsers
            this.loadAllImagesImmediately();
        }

        this.setupEventListeners();
    }

    setupImageObserver() {
        this.imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadImage(entry.target);
                    this.imageObserver.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: this.options.rootMargin,
            threshold: this.options.threshold
        });
    }

    setupComponentObserver() {
        this.componentObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadComponent(entry.target);
                    this.componentObserver.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: this.options.rootMargin,
            threshold: this.options.threshold
        });
    }

    observeElements() {
        // Observe images
        const images = document.querySelectorAll(this.options.imageSelector);
        images.forEach(img => {
            if (!this.loadedImages.has(img)) {
                this.setupImagePlaceholder(img);
                this.imageObserver.observe(img);
            }
        });

        // Observe components
        const components = document.querySelectorAll(this.options.componentSelector);
        components.forEach(component => {
            if (!this.loadedComponents.has(component)) {
                this.componentObserver.observe(component);
            }
        });
    }

    setupImagePlaceholder(img) {
        if (!this.options.enablePlaceholder) return;

        const width = img.getAttribute('width') || img.offsetWidth || 300;
        const height = img.getAttribute('height') || img.offsetHeight || 200;
        
        // Create placeholder with blur effect
        const placeholder = this.createPlaceholder(width, height);
        img.style.background = `url(${placeholder}) center/cover`;
        img.style.backgroundColor = '#f0f0f0';
        img.style.minHeight = height + 'px';
    }

    createPlaceholder(width, height) {
        // Create a simple SVG placeholder
        const svg = `
            <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#f0f0f0"/>
                <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#999" font-family="Arial, sans-serif" font-size="14">
                    Loading...
                </text>
            </svg>
        `;
        return 'data:image/svg+xml;base64,' + btoa(svg);
    }

    loadImage(img) {
        const src = img.getAttribute('data-src');
        const srcset = img.getAttribute('data-srcset');
        
        if (!src) return;

        // Create a new image to preload
        const imageLoader = new Image();
        
        imageLoader.onload = () => {
            // Set the actual source
            img.src = src;
            if (srcset) {
                img.srcset = srcset;
            }

            // Remove data attributes
            img.removeAttribute('data-src');
            img.removeAttribute('data-srcset');

            // Add fade-in effect
            this.fadeInImage(img);
            
            // Mark as loaded
            this.loadedImages.add(img);
            
            // Trigger custom event
            img.dispatchEvent(new CustomEvent('lazyloaded', {
                detail: { src: src }
            }));
        };

        imageLoader.onerror = () => {
            // Handle error - show error placeholder
            img.src = this.createErrorPlaceholder();
            img.classList.add('lazy-error');
        };

        // Start loading
        imageLoader.src = src;
    }

    loadComponent(component) {
        const componentType = component.getAttribute('data-lazy-component');
        const componentData = component.getAttribute('data-component-data');
        
        try {
            const data = componentData ? JSON.parse(componentData) : {};
            
            switch (componentType) {
                case 'datatable':
                    this.loadDataTable(component, data);
                    break;
                case 'chart':
                    this.loadChart(component, data);
                    break;
                case 'modal':
                    this.loadModal(component, data);
                    break;
                default:
                    this.loadGenericComponent(component, data);
            }
            
            this.loadedComponents.add(component);
            
            // Trigger custom event
            component.dispatchEvent(new CustomEvent('componentloaded', {
                detail: { type: componentType, data: data }
            }));
            
        } catch (error) {
            console.error('Failed to load component:', error);
            component.innerHTML = '<div class="alert alert-danger">Failed to load component</div>';
        }
    }

    loadDataTable(component, data) {
        // Show loading spinner
        component.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"></div></div>';
        
        // Load DataTable asynchronously
        if (typeof $ !== 'undefined' && $.fn.DataTable) {
            const tableHtml = `<table class="table table-striped" id="${data.id || 'lazy-table'}"></table>`;
            component.innerHTML = tableHtml;
            
            // Initialize DataTable with provided options
            $(`#${data.id || 'lazy-table'}`).DataTable(data.options || {});
        }
    }

    loadChart(component, data) {
        // Placeholder for chart loading
        component.innerHTML = '<div class="text-center p-4">Loading chart...</div>';
        
        // This would integrate with your chart library (Chart.js, etc.)
        setTimeout(() => {
            component.innerHTML = '<div class="alert alert-info">Chart would be loaded here</div>';
        }, 1000);
    }

    loadModal(component, data) {
        // Load modal content via AJAX
        if (data.url) {
            fetch(data.url)
                .then(response => response.text())
                .then(html => {
                    component.innerHTML = html;
                })
                .catch(error => {
                    component.innerHTML = '<div class="alert alert-danger">Failed to load modal content</div>';
                });
        }
    }

    loadGenericComponent(component, data) {
        // Generic component loading
        if (data.url) {
            fetch(data.url)
                .then(response => response.text())
                .then(html => {
                    component.innerHTML = html;
                })
                .catch(error => {
                    component.innerHTML = '<div class="alert alert-danger">Failed to load content</div>';
                });
        }
    }

    fadeInImage(img) {
        img.style.opacity = '0';
        img.style.transition = `opacity ${this.options.fadeInDuration}ms ease-in-out`;
        
        // Force reflow
        img.offsetHeight;
        
        img.style.opacity = '1';
        
        // Clean up after animation
        setTimeout(() => {
            img.style.transition = '';
        }, this.options.fadeInDuration);
    }

    createErrorPlaceholder() {
        const svg = `
            <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#f8f9fa"/>
                <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#dc3545" font-family="Arial, sans-serif" font-size="14">
                    Failed to load image
                </text>
            </svg>
        `;
        return 'data:image/svg+xml;base64,' + btoa(svg);
    }

    loadAllImagesImmediately() {
        // Fallback for browsers without IntersectionObserver
        const images = document.querySelectorAll(this.options.imageSelector);
        images.forEach(img => {
            const src = img.getAttribute('data-src');
            if (src) {
                img.src = src;
                img.removeAttribute('data-src');
            }
        });
    }

    setupEventListeners() {
        // Re-observe new elements added to DOM
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check for new images
                        const newImages = node.querySelectorAll ? 
                            node.querySelectorAll(this.options.imageSelector) : [];
                        newImages.forEach(img => {
                            if (!this.loadedImages.has(img)) {
                                this.setupImagePlaceholder(img);
                                this.imageObserver.observe(img);
                            }
                        });

                        // Check for new components
                        const newComponents = node.querySelectorAll ? 
                            node.querySelectorAll(this.options.componentSelector) : [];
                        newComponents.forEach(component => {
                            if (!this.loadedComponents.has(component)) {
                                this.componentObserver.observe(component);
                            }
                        });
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Public methods
    refresh() {
        this.observeElements();
    }

    loadAll() {
        const images = document.querySelectorAll(this.options.imageSelector);
        images.forEach(img => this.loadImage(img));

        const components = document.querySelectorAll(this.options.componentSelector);
        components.forEach(component => this.loadComponent(component));
    }

    destroy() {
        if (this.imageObserver) {
            this.imageObserver.disconnect();
        }
        if (this.componentObserver) {
            this.componentObserver.disconnect();
        }
    }
}

// Initialize lazy loading when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.lazyLoader = new LazyLoader({
        imageSelector: 'img[data-src], img[loading="lazy"]',
        componentSelector: '[data-lazy-component]',
        rootMargin: '100px',
        threshold: 0.1,
        enablePlaceholder: true,
        fadeInDuration: 300
    });
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LazyLoader;
}
