<?php $__env->startSection('title', 'Products Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex flex-column flex-column-fluid">
    
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Products</h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="<?php echo e(route('admin-dashboard')); ?>" class="text-muted text-hover-primary">Home</a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-400 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">Ecommerce</li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-400 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">Products</li>
                </ul>
            </div>
            <div class="d-flex align-items-center gap-2 gap-lg-3">
                <a href="<?php echo e(route('products.create')); ?>" class="btn btn-sm fw-bold btn-primary">
                    <i class="fa fa-plus"></i> Add Product
                </a>
            </div>
        </div>
    </div>

    
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            
            
            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fa fa-check-circle me-2"></i><?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fa fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            
            <div class="card">
                
                <div class="card-header border-0 pt-6">
                    <div class="card-title">
                        <div class="d-flex align-items-center position-relative my-1">
                            <i class="fa fa-search fs-3 position-absolute ms-5"></i>
                            <input type="text" data-kt-products-table-filter="search" class="form-control form-control-solid w-250px ps-13" placeholder="Search products..." />
                        </div>
                    </div>
                    <div class="card-toolbar">
                        <div class="d-flex justify-content-end" data-kt-products-table-toolbar="base">
                            <button type="button" class="btn btn-light-primary me-3" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                <i class="fa fa-filter fs-2"></i> Filter
                            </button>
                            <div class="menu menu-sub menu-sub-dropdown w-300px w-md-325px" data-kt-menu="true">
                                <div class="px-7 py-5">
                                    <div class="fs-5 text-dark fw-bold">Filter Options</div>
                                </div>
                                <div class="separator border-gray-200"></div>
                                <div class="px-7 py-5" data-kt-products-table-filter="form">
                                    <div class="mb-10">
                                        <label class="form-label fs-6 fw-semibold">Status:</label>
                                        <select class="form-select form-select-solid fw-bold" data-kt-select2="true" data-placeholder="Select option" data-allow-clear="true" data-kt-products-table-filter="status">
                                            <option></option>
                                            <option value="active">Active</option>
                                            <option value="inactive">Inactive</option>
                                            <option value="draft">Draft</option>
                                        </select>
                                    </div>
                                    <div class="d-flex justify-content-end">
                                        <button type="reset" class="btn btn-light btn-active-light-primary fw-semibold me-2 px-6" data-kt-menu-dismiss="true" data-kt-products-table-filter="reset">Reset</button>
                                        <button type="submit" class="btn btn-primary fw-semibold px-6" data-kt-menu-dismiss="true" data-kt-products-table-filter="filter">Apply</button>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#kt_modal_bulk_actions">
                                <i class="fa fa-layer-group fs-2"></i> Bulk Actions
                            </button>
                        </div>
                    </div>
                </div>

                
                <div class="card-body py-4">
                    <div class="table-responsive">
                        <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_products_table">
                            <thead>
                                <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                    <th class="w-10px pe-2">
                                        <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                            <input class="form-check-input" type="checkbox" data-kt-check="true" data-kt-check-target="#kt_products_table .form-check-input" value="1" />
                                        </div>
                                    </th>
                                    <th class="min-w-125px">Product</th>
                                    <th class="min-w-125px">SKU</th>
                                    <th class="min-w-125px">Category</th>
                                    <th class="min-w-125px">Brand</th>
                                    <th class="min-w-125px">Price</th>
                                    <th class="min-w-125px">Stock</th>
                                    <th class="min-w-125px">Status</th>
                                    <th class="text-end min-w-100px">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="text-gray-600 fw-semibold">
                                <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div class="form-check form-check-sm form-check-custom form-check-solid">
                                            <input class="form-check-input" type="checkbox" value="<?php echo e($product->id); ?>" />
                                        </div>
                                    </td>
                                    <td class="d-flex align-items-center">
                                        <div class="symbol symbol-circle symbol-50px overflow-hidden me-3">
                                            <div class="symbol-label">
                                                <?php if($product->getMainImage()): ?>
                                                    <img src="<?php echo e($product->getMainImage()); ?>" alt="<?php echo e($product->name); ?>" class="w-100" />
                                                <?php else: ?>
                                                    <div class="symbol-label fs-3 bg-light-primary text-primary">
                                                        <?php echo e(substr($product->name, 0, 1)); ?>

                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="d-flex flex-column">
                                            <a href="<?php echo e(route('products.show', $product)); ?>" class="text-gray-800 text-hover-primary mb-1"><?php echo e($product->name); ?></a>
                                            <span class="text-muted"><?php echo e(Str::limit($product->short_description, 50)); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-light-info"><?php echo e($product->sku); ?></span>
                                    </td>
                                    <td>
                                        <?php if($product->category): ?>
                                            <span class="badge badge-light-success"><?php echo e($product->category->category); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">No Category</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($product->brand): ?>
                                            <span class="badge badge-light-warning"><?php echo e($product->brand->name); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">No Brand</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-bold">$<?php echo e(number_format($product->price, 2)); ?></span>
                                            <?php if($product->compare_price): ?>
                                                <span class="text-muted text-decoration-line-through fs-7">$<?php echo e(number_format($product->compare_price, 2)); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="fw-bold me-2"><?php echo e($product->stock_quantity); ?></span>
                                            <?php if($product->stock_status === 'low_stock'): ?>
                                                <span class="badge badge-light-warning">Low</span>
                                            <?php elseif($product->stock_status === 'out_of_stock'): ?>
                                                <span class="badge badge-light-danger">Out</span>
                                            <?php else: ?>
                                                <span class="badge badge-light-success">In Stock</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($product->status === 'active'): ?>
                                            <span class="badge badge-light-success">Active</span>
                                        <?php elseif($product->status === 'inactive'): ?>
                                            <span class="badge badge-light-danger">Inactive</span>
                                        <?php else: ?>
                                            <span class="badge badge-light-warning">Draft</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-end">
                                        <a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                            Actions
                                            <i class="fa fa-angle-down fs-5 ms-1"></i>
                                        </a>
                                        <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">
                                            <div class="menu-item px-3">
                                                <a href="<?php echo e(route('products.show', $product)); ?>" class="menu-link px-3">View</a>
                                            </div>
                                            <div class="menu-item px-3">
                                                <a href="<?php echo e(route('products.edit', $product)); ?>" class="menu-link px-3">Edit</a>
                                            </div>
                                            <div class="menu-item px-3">
                                                <a href="#" class="menu-link px-3" data-kt-products-table-filter="delete_row" data-product-id="<?php echo e($product->id); ?>">Delete</a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="9" class="text-center py-10">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fa fa-box-open fs-2x text-muted mb-3"></i>
                                            <span class="text-muted fs-4">No products found</span>
                                            <a href="<?php echo e(route('products.create')); ?>" class="btn btn-primary mt-3">
                                                <i class="fa fa-plus"></i> Add Your First Product
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    
                    <?php if($products->hasPages()): ?>
                        <div class="d-flex justify-content-center mt-5">
                            <?php echo e($products->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="kt_modal_bulk_actions" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered mw-650px">
        <div class="modal-content">
            <form id="kt_modal_bulk_actions_form" class="form" action="<?php echo e(route('products.bulk-action')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-header">
                    <h2 class="fw-bold">Bulk Actions</h2>
                    <div class="btn btn-icon btn-sm btn-active-icon-primary" data-bs-dismiss="modal">
                        <i class="fa fa-times fs-1"></i>
                    </div>
                </div>
                <div class="modal-body scroll-y mx-5 mx-xl-15 my-7">
                    <div class="mb-10">
                        <label class="form-label fs-6 fw-semibold">Select Action:</label>
                        <select class="form-select form-select-solid" name="action" required>
                            <option value="">Choose action...</option>
                            <option value="activate">Activate Selected</option>
                            <option value="deactivate">Deactivate Selected</option>
                            <option value="delete">Delete Selected</option>
                        </select>
                    </div>
                    <input type="hidden" name="product_ids" id="selected_product_ids">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Apply Action</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle bulk actions
    const bulkActionForm = document.getElementById('kt_modal_bulk_actions_form');
    const selectedIdsInput = document.getElementById('selected_product_ids');
    
    // Update selected IDs when modal opens
    document.getElementById('kt_modal_bulk_actions').addEventListener('show.bs.modal', function() {
        const checkedBoxes = document.querySelectorAll('#kt_products_table input[type="checkbox"]:checked:not([data-kt-check])');
        const ids = Array.from(checkedBoxes).map(cb => cb.value);
        selectedIdsInput.value = JSON.stringify(ids);
    });
    
    // Handle search
    const searchInput = document.querySelector('[data-kt-products-table-filter="search"]');
    if (searchInput) {
        searchInput.addEventListener('keyup', function(e) {
            // Implement search functionality
            console.log('Search:', e.target.value);
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\arclok_admin\resources\views/products/index.blade.php ENDPATH**/ ?>