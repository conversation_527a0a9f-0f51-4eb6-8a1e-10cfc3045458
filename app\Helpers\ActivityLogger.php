<?php

namespace App\Helpers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;

class ActivityLogger
{
    /**
     * Activity types
     */
    const TYPES = [
        'login' => 'User Login',
        'logout' => 'User Logout',
        'create' => 'Create Record',
        'update' => 'Update Record',
        'delete' => 'Delete Record',
        'view' => 'View Record',
        'export' => 'Export Data',
        'import' => 'Import Data',
        'upload' => 'File Upload',
        'download' => 'File Download',
        'search' => 'Search Action',
        'filter' => 'Filter Action',
        'settings' => 'Settings Change',
        'security' => 'Security Event',
        'error' => 'Error Occurred',
    ];

    /**
     * Log user activity
     *
     * @param string $type
     * @param string $description
     * @param array $metadata
     * @param int|null $userId
     * @return bool
     */
    public static function log(string $type, string $description, array $metadata = [], ?int $userId = null): bool
    {
        try {
            // Check if user_activities table exists
            if (!Schema::hasTable('user_activities')) {
                return false;
            }

            $userId = $userId ?? Auth::id();
            
            $activity = [
                'user_id' => $userId,
                'type' => $type,
                'description' => $description,
                'metadata' => json_encode($metadata),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'route' => request()->route()?->getName(),
                'method' => request()->method(),
                'url' => request()->url(),
                'created_at' => now(),
                'updated_at' => now(),
            ];

            DB::table('user_activities')->insert($activity);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('Failed to log user activity: ' . $e->getMessage(), [
                'type' => $type,
                'description' => $description,
                'metadata' => $metadata
            ]);
            return false;
        }
    }

    /**
     * Log login activity
     *
     * @param int|null $userId
     * @param bool $successful
     * @return bool
     */
    public static function logLogin(?int $userId = null, bool $successful = true): bool
    {
        $description = $successful ? 'User logged in successfully' : 'Failed login attempt';
        
        return self::log('login', $description, [
            'successful' => $successful,
            'timestamp' => now()->toISOString()
        ], $userId);
    }

    /**
     * Log logout activity
     *
     * @param int|null $userId
     * @return bool
     */
    public static function logLogout(?int $userId = null): bool
    {
        return self::log('logout', 'User logged out', [
            'timestamp' => now()->toISOString()
        ], $userId);
    }

    /**
     * Log CRUD operations
     *
     * @param string $operation
     * @param string $model
     * @param int|string $recordId
     * @param array $changes
     * @param int|null $userId
     * @return bool
     */
    public static function logCrud(string $operation, string $model, $recordId, array $changes = [], ?int $userId = null): bool
    {
        $description = ucfirst($operation) . " {$model} record";
        
        $metadata = [
            'model' => $model,
            'record_id' => $recordId,
            'operation' => $operation,
            'changes' => $changes,
            'timestamp' => now()->toISOString()
        ];

        return self::log($operation, $description, $metadata, $userId);
    }

    /**
     * Log file operations
     *
     * @param string $operation
     * @param string $filename
     * @param string $path
     * @param int|null $fileSize
     * @param int|null $userId
     * @return bool
     */
    public static function logFileOperation(string $operation, string $filename, string $path, ?int $fileSize = null, ?int $userId = null): bool
    {
        $description = ucfirst($operation) . " file: {$filename}";
        
        $metadata = [
            'filename' => $filename,
            'path' => $path,
            'file_size' => $fileSize,
            'operation' => $operation,
            'timestamp' => now()->toISOString()
        ];

        return self::log($operation, $description, $metadata, $userId);
    }

    /**
     * Log search activity
     *
     * @param string $query
     * @param string $model
     * @param int $resultsCount
     * @param int|null $userId
     * @return bool
     */
    public static function logSearch(string $query, string $model, int $resultsCount, ?int $userId = null): bool
    {
        $description = "Searched {$model} for: {$query}";
        
        $metadata = [
            'query' => $query,
            'model' => $model,
            'results_count' => $resultsCount,
            'timestamp' => now()->toISOString()
        ];

        return self::log('search', $description, $metadata, $userId);
    }

    /**
     * Log export activity
     *
     * @param string $type
     * @param string $filename
     * @param int $recordCount
     * @param int|null $userId
     * @return bool
     */
    public static function logExport(string $type, string $filename, int $recordCount, ?int $userId = null): bool
    {
        $description = "Exported {$recordCount} records to {$filename}";
        
        $metadata = [
            'export_type' => $type,
            'filename' => $filename,
            'record_count' => $recordCount,
            'timestamp' => now()->toISOString()
        ];

        return self::log('export', $description, $metadata, $userId);
    }

    /**
     * Log security events
     *
     * @param string $event
     * @param string $description
     * @param array $details
     * @param int|null $userId
     * @return bool
     */
    public static function logSecurity(string $event, string $description, array $details = [], ?int $userId = null): bool
    {
        $metadata = array_merge($details, [
            'security_event' => $event,
            'severity' => $details['severity'] ?? 'medium',
            'timestamp' => now()->toISOString()
        ]);

        return self::log('security', $description, $metadata, $userId);
    }

    /**
     * Log error events
     *
     * @param string $error
     * @param string $context
     * @param array $details
     * @param int|null $userId
     * @return bool
     */
    public static function logError(string $error, string $context, array $details = [], ?int $userId = null): bool
    {
        $description = "Error in {$context}: {$error}";
        
        $metadata = array_merge($details, [
            'error_message' => $error,
            'context' => $context,
            'timestamp' => now()->toISOString()
        ]);

        return self::log('error', $description, $metadata, $userId);
    }

    /**
     * Get user activities
     *
     * @param int|null $userId
     * @param int $limit
     * @param array $types
     * @return array
     */
    public static function getUserActivities(?int $userId = null, int $limit = 50, array $types = []): array
    {
        try {
            if (!Schema::hasTable('user_activities')) {
                return [];
            }

            $query = DB::table('user_activities')
                ->select('*')
                ->orderBy('created_at', 'desc')
                ->limit($limit);

            if ($userId) {
                $query->where('user_id', $userId);
            }

            if (!empty($types)) {
                $query->whereIn('type', $types);
            }

            return $query->get()->toArray();
            
        } catch (\Exception $e) {
            Log::error('Failed to get user activities: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get activity statistics
     *
     * @param int|null $userId
     * @param int $days
     * @return array
     */
    public static function getActivityStats(?int $userId = null, int $days = 30): array
    {
        try {
            if (!Schema::hasTable('user_activities')) {
                return [];
            }

            $since = now()->subDays($days);
            
            $query = DB::table('user_activities')
                ->where('created_at', '>=', $since);

            if ($userId) {
                $query->where('user_id', $userId);
            }

            $stats = [
                'total_activities' => $query->count(),
                'activities_by_type' => $query->select('type', DB::raw('count(*) as count'))
                    ->groupBy('type')
                    ->pluck('count', 'type')
                    ->toArray(),
                'activities_by_day' => $query->select(
                        DB::raw('DATE(created_at) as date'),
                        DB::raw('count(*) as count')
                    )
                    ->groupBy('date')
                    ->orderBy('date')
                    ->pluck('count', 'date')
                    ->toArray(),
                'most_active_users' => !$userId ? 
                    $query->select('user_id', DB::raw('count(*) as count'))
                        ->groupBy('user_id')
                        ->orderBy('count', 'desc')
                        ->limit(10)
                        ->pluck('count', 'user_id')
                        ->toArray() : [],
            ];

            return $stats;
            
        } catch (\Exception $e) {
            Log::error('Failed to get activity statistics: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Clean up old activities
     *
     * @param int $days
     * @return int
     */
    public static function cleanup(int $days = 90): int
    {
        try {
            if (!Schema::hasTable('user_activities')) {
                return 0;
            }

            $cutoff = now()->subDays($days);
            
            return DB::table('user_activities')
                ->where('created_at', '<', $cutoff)
                ->delete();
                
        } catch (\Exception $e) {
            Log::error('Failed to cleanup user activities: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get recent activities for dashboard
     *
     * @param int $limit
     * @return array
     */
    public static function getRecentActivities(int $limit = 10): array
    {
        try {
            if (!Schema::hasTable('user_activities')) {
                return [];
            }

            return DB::table('user_activities')
                ->leftJoin('users', 'user_activities.user_id', '=', 'users.id')
                ->select(
                    'user_activities.*',
                    'users.name as user_name',
                    'users.email as user_email'
                )
                ->orderBy('user_activities.created_at', 'desc')
                ->limit($limit)
                ->get()
                ->map(function ($activity) {
                    $activity->metadata = json_decode($activity->metadata, true);
                    $activity->type_label = self::TYPES[$activity->type] ?? ucfirst($activity->type);
                    return $activity;
                })
                ->toArray();
                
        } catch (\Exception $e) {
            Log::error('Failed to get recent activities: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Create user activities table migration
     *
     * @return string
     */
    public static function getMigrationCode(): string
    {
        return '
Schema::create("user_activities", function (Blueprint $table) {
    $table->id();
    $table->unsignedBigInteger("user_id")->nullable();
    $table->string("type", 50);
    $table->text("description");
    $table->json("metadata")->nullable();
    $table->string("ip_address", 45)->nullable();
    $table->text("user_agent")->nullable();
    $table->string("route")->nullable();
    $table->string("method", 10)->nullable();
    $table->text("url")->nullable();
    $table->timestamps();
    
    $table->index(["user_id", "created_at"]);
    $table->index(["type", "created_at"]);
    $table->index("created_at");
    
    $table->foreign("user_id")->references("id")->on("users")->onDelete("set null");
});
        ';
    }
}
