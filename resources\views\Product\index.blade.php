@extends('Layouts.app')
@section('title', 'Products')
@section('content')
<div class="container-fluid py-4">
    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $total_products }}</h4>
                            <p class="mb-0">Total Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $active_products }}</h4>
                            <p class="mb-0">Active Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $featured_products }}</h4>
                            <p class="mb-0">Featured Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-star fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $out_of_stock }}</h4>
                            <p class="mb-0">Out of Stock</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Products</h4>
            <button class="btn btn-primary" id="addProductBtn">Add Product</button>
        </div>
        <div class="card-body">
            <table id="productTable" class="table table-bordered table-striped dt-responsive nowrap w-100">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>SKU</th>
                        <th>Category</th>
                        <th>Amount</th>
                        <th>Stock</th>
                        <th>Status</th>
                        <th>Featured</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
    </div>
</div>
@include('Product.modal')
@endsection
@section('scripts')
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#productTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("products.index") }}',
            type: 'GET'
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'sku', name: 'sku' },
            { data: 'category_name', name: 'category_name', orderable: false },
            { data: 'formatted_amount', name: 'amount' },
            { data: 'stock_status', name: 'stock_quantity' },
            { data: 'status_badge', name: 'status' },
            { data: 'featured_badge', name: 'is_featured' },
            { data: 'created_at', name: 'created_at' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[8, 'desc']],
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
        language: {
            processing: '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>',
            emptyTable: "No products found",
            zeroRecords: "No matching products found"
        }
    });

    // Add product button
    $('#addProductBtn').on('click', function() {
        $('#productForm')[0].reset();
        $('#product_id').val('');
        $('#productModalLabel').text('Add Product');
        $('#productModal').modal('show');
    });

    // Edit product
    $(document).on('click', '.btn-warning', function(e) {
        e.preventDefault();
        var href = $(this).attr('href');
        var id = href.split('/').pop();

        showLoading('Loading product...');
        $.ajax({
            url: '/products/' + id + '/edit',
            type: 'GET',
            success: function(res) {
                hideLoading();
                if (res.success) {
                    var product = res.data;
                    $('#product_id').val(product.id);
                    $('#name').val(product.name);
                    $('#slug').val(product.slug);
                    $('#description').val(product.description);
                    $('#short_description').val(product.short_description);
                    $('#price').val(product.amount);
                    $('#category_id').val(product.category_id);
                    $('#sku').val(product.sku);
                    $('#status').val(product.status);
                    $('#weight').val(product.weight);
                    $('#stock_quantity').val(product.stock_quantity);
                    $('#cost_price').val(product.cost_price);
                    $('#track_inventory').prop('checked', product.track_inventory);
                    $('#is_featured').prop('checked', product.is_featured);

                    $('#productModalLabel').text('Edit Product');
                    $('#productModal').modal('show');
                } else {
                    showError('Error', res.message || 'Failed to load product');
                }
            },
            error: function(xhr) {
                hideLoading();
                showError('Error', xhr.responseJSON?.message || 'Failed to load product');
            }
        });
    });

    // Save (add/update) product
    $('#productForm').on('submit', function(e) {
        e.preventDefault();
        var id = $('#product_id').val();
        var url = id ? '/products/' + id : '/products';
        var method = id ? 'PUT' : 'POST';
        showLoading('Saving product...');
        $.ajax({
            url: url,
            type: method,
            data: $(this).serialize(),
            headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
            success: function(res) {
                hideLoading();
                if (res.success) {
                    $('#productModal').modal('hide');
                    showSuccess('Success', res.message);
                    table.ajax.reload(null, false);
                } else {
                    showError('Error', res.message || 'Failed to save product');
                }
            },
            error: function(xhr) {
                hideLoading();
                showError('Error', xhr.responseJSON?.message || 'Failed to save product');
            }
        });
    });

    // Delete product
    $(document).on('click', '.delete-products', function() {
        var id = $(this).data('id');

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                showLoading('Deleting product...');
                $.ajax({
                    url: '/products/' + id,
                    type: 'DELETE',
                    headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                    success: function(res) {
                        hideLoading();
                        if (res.success) {
                            showSuccess('Success', res.message);
                            table.ajax.reload(null, false);
                        } else {
                            showError('Error', res.message || 'Failed to delete product');
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        showError('Error', xhr.responseJSON?.message || 'Failed to delete product');
                    }
                });
            }
        });
    });
});
</script>
@endsection
