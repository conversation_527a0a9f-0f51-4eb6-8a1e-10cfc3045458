<?php $__env->startSection('title', $title ?? 'Coming Soon'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex flex-column flex-column-fluid">
    
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0"><?php echo e($title ?? 'Coming Soon'); ?></h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="<?php echo e(route('admin-dashboard')); ?>" class="text-muted text-hover-primary">Home</a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-400 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted"><?php echo e($section ?? 'Ecommerce'); ?></li>
                    <?php if(isset($subsection)): ?>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-400 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted"><?php echo e($subsection); ?></li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>

    
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <div class="card">
                <div class="card-body text-center py-20">
                    <div class="mb-10">
                        <i class="fa fa-<?php echo e($icon ?? 'cogs'); ?> fs-4x text-primary mb-10"></i>
                    </div>
                    
                    <h1 class="fw-bolder fs-2hx text-gray-900 mb-4"><?php echo e($title ?? 'Coming Soon'); ?></h1>
                    
                    <div class="fw-semibold fs-6 text-gray-500 mb-7">
                        <?php echo e($message ?? 'This module is currently under development and will be available soon.'); ?>

                    </div>

                    <?php if(isset($features) && is_array($features)): ?>
                    <div class="mb-10">
                        <h3 class="fw-bold text-gray-900 mb-5">Planned Features:</h3>
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <div class="d-flex flex-column text-start">
                                    <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="fa fa-check-circle text-success fs-4 me-3"></i>
                                        <span class="fw-semibold text-gray-700"><?php echo e($feature); ?></span>
                                    </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="mb-0">
                        <a href="<?php echo e(route('admin-dashboard')); ?>" class="btn btn-lg btn-primary me-3">
                            <i class="fa fa-arrow-left"></i> Back to Dashboard
                        </a>
                        
                        <?php if(isset($availableModules) && is_array($availableModules)): ?>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-lg btn-light-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fa fa-th-large"></i> Available Modules
                            </button>
                            <ul class="dropdown-menu">
                                <?php $__currentLoopData = $availableModules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><a class="dropdown-item" href="<?php echo e($module['url']); ?>">
                                    <i class="fa fa-<?php echo e($module['icon']); ?> me-2"></i><?php echo e($module['name']); ?>

                                </a></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            
            <?php if(isset($progress)): ?>
            <div class="card mt-10">
                <div class="card-header">
                    <h3 class="card-title">Development Progress</h3>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-5">
                        <span class="fw-bold text-gray-800 me-2">Overall Progress:</span>
                        <span class="fw-bold text-primary"><?php echo e($progress); ?>%</span>
                    </div>
                    <div class="progress h-15px">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: <?php echo e($progress); ?>%" aria-valuenow="<?php echo e($progress); ?>" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    
                    <?php if(isset($progressDetails) && is_array($progressDetails)): ?>
                    <div class="mt-8">
                        <?php $__currentLoopData = $progressDetails; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <span class="text-gray-700"><?php echo e($detail['name']); ?></span>
                            <div class="d-flex align-items-center">
                                <span class="text-muted me-3"><?php echo e($detail['progress']); ?>%</span>
                                <div class="progress h-8px w-100px">
                                    <div class="progress-bar bg-<?php echo e($detail['status'] === 'completed' ? 'success' : ($detail['status'] === 'in_progress' ? 'warning' : 'light')); ?>" 
                                         role="progressbar" 
                                         style="width: <?php echo e($detail['progress']); ?>%" 
                                         aria-valuenow="<?php echo e($detail['progress']); ?>" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\arclok_admin\resources\views/coming-soon.blade.php ENDPATH**/ ?>