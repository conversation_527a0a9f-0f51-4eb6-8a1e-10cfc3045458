/* ================================ SIDEBAR ACTIVE STATES ================================ */

/* Active menu link styling with grey transparent background */
.menu-link.active {
    background-color: rgba(128, 128, 128, 0.15) !important;
    color: #ffffff !important;
    border-radius: 0.475rem;
    position: relative;
    transition: all 0.3s ease;
}

.menu-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 60%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 0 2px 2px 0;
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.3);
}

/* Active menu icon styling */
.menu-link.active .menu-icon {
    color: #ffffff !important;
    opacity: 1;
}

.menu-link.active .menu-icon i,
.menu-link.active .menu-icon .svg-icon {
    color: #ffffff !important;
    filter: brightness(1.2);
}

/* Active menu title styling */
.menu-link.active .menu-title {
    color: #ffffff !important;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Hover effects for non-active menu items */
.menu-link:not(.active):hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
    color: #e0e0e0 !important;
    border-radius: 0.475rem;
    transition: all 0.3s ease;
}

.menu-link:not(.active):hover .menu-icon {
    color: #e0e0e0 !important;
    transform: scale(1.05);
    transition: all 0.3s ease;
}

.menu-link:not(.active):hover .menu-title {
    color: #e0e0e0 !important;
}

/* Parent menu (accordion) active state */
.menu-item.menu-accordion.has-active-child > .menu-link {
    background-color: rgba(128, 128, 128, 0.1) !important;
    color: #ffffff !important;
    border-radius: 0.475rem;
}

.menu-item.menu-accordion.has-active-child > .menu-link .menu-title {
    color: #ffffff !important;
    font-weight: 600;
}

.menu-item.menu-accordion.has-active-child > .menu-link .menu-icon {
    color: #ffffff !important;
}

.menu-item.menu-accordion.has-active-child > .menu-link .menu-arrow {
    color: #ffffff !important;
}

/* Submenu active states */
.menu-sub .menu-item .menu-link.active {
    background-color: rgba(128, 128, 128, 0.2) !important;
    color: #ffffff !important;
    border-radius: 0.375rem;
    margin-left: 0.5rem;
    position: relative;
}

.menu-sub .menu-item .menu-link.active::before {
    content: '';
    position: absolute;
    left: -0.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 70%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 1px;
}

.menu-sub .menu-item .menu-link.active .menu-bullet .bullet {
    background-color: #007bff !important;
    box-shadow: 0 0 6px rgba(0, 123, 255, 0.4);
}

.menu-sub .menu-item .menu-link.active .menu-title {
    color: #ffffff !important;
    font-weight: 600;
}

/* Submenu hover effects */
.menu-sub .menu-item .menu-link:not(.active):hover {
    background-color: rgba(255, 255, 255, 0.03) !important;
    color: #e0e0e0 !important;
    border-radius: 0.375rem;
    margin-left: 0.25rem;
    transition: all 0.3s ease;
}

.menu-sub .menu-item .menu-link:not(.active):hover .menu-bullet .bullet {
    background-color: #e0e0e0 !important;
    transform: scale(1.1);
}

/* Expanded accordion styling */
.menu-item.menu-accordion.show > .menu-link .menu-arrow {
    transform: rotate(90deg);
    transition: transform 0.3s ease;
}

.menu-item.menu-accordion:not(.show) > .menu-link .menu-arrow {
    transform: rotate(0deg);
    transition: transform 0.3s ease;
}

/* Smooth accordion animation */
.menu-sub.menu-sub-accordion {
    transition: all 0.3s ease;
    overflow: hidden;
}

.menu-sub.menu-sub-accordion:not(.show) {
    max-height: 0;
    opacity: 0;
    padding-top: 0;
    padding-bottom: 0;
}

.menu-sub.menu-sub-accordion.show {
    max-height: 500px;
    opacity: 1;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

/* Focus states for accessibility */
.menu-link:focus {
    outline: 2px solid rgba(0, 123, 255, 0.5);
    outline-offset: 2px;
    border-radius: 0.475rem;
}

/* Active state animations */
@keyframes activeMenuPulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4); }
    70% { box-shadow: 0 0 0 6px rgba(0, 123, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
}

.menu-link.active {
    animation: activeMenuPulse 2s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .menu-link.active::before {
        width: 2px;
        height: 50%;
    }
    
    .menu-sub .menu-item .menu-link.active {
        margin-left: 0.25rem;
    }
    
    .menu-sub .menu-item .menu-link.active::before {
        left: -0.25rem;
        width: 1.5px;
    }
}

/* Dark theme adjustments */
[data-theme="dark"] .menu-link.active {
    background-color: rgba(200, 200, 200, 0.1) !important;
}

[data-theme="dark"] .menu-link:not(.active):hover {
    background-color: rgba(255, 255, 255, 0.08) !important;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .menu-link.active {
        background-color: rgba(128, 128, 128, 0.3) !important;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .menu-link.active::before {
        background: #ffffff;
        width: 4px;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .menu-link,
    .menu-link.active,
    .menu-arrow,
    .menu-sub.menu-sub-accordion {
        transition: none !important;
        animation: none !important;
    }
}

/* Print styles */
@media print {
    .menu-link.active::before,
    .menu-sub .menu-item .menu-link.active::before {
        display: none;
    }
    
    .menu-link.active {
        background-color: transparent !important;
        color: #000000 !important;
        text-decoration: underline;
    }
}
