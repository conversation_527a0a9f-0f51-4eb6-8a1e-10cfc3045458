<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductCategory;
use App\Helpers\Helper;
use App\Helpers\DatabaseHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Str;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (request()->ajax()) {
            return DatabaseHelper::monitoredQuery(function () {
                // Optimize query with specific columns and indexes
                $query = Product::with('category:id,category')
                    ->select(['id', 'name', 'slug', 'sku', 'amount', 'category_id', 'status', 'stock_quantity', 'is_featured', 'created_at'])
                    ->orderBy('created_at', 'desc');

                // Apply DataTables optimizations
                $query = DatabaseHelper::optimizeDataTablesQuery(
                    $query,
                    ['id', 'name', 'sku', 'amount', 'category_id', 'status', 'stock_quantity', 'is_featured', 'created_at'],
                    ['name', 'sku']
                );

                return DataTables::of($query)
                    ->addIndexColumn()
                    ->addColumn('category_name', function($row) {
                        return $row->category ? $row->category->category : 'No Category';
                    })
                    ->addColumn('status_badge', function($row) {
                        return Helper::getStatusBadge($row->status === 'active' ? 1 : 0);
                    })
                    ->addColumn('featured_badge', function($row) {
                        return $row->is_featured ? '<span class="badge badge-warning">Featured</span>' : '<span class="badge badge-light">Regular</span>';
                    })
                    ->addColumn('formatted_amount', function($row) {
                        return '$' . number_format($row->amount, 2);
                    })
                    ->addColumn('stock_status', function($row) {
                        if ($row->stock_quantity > 10) {
                            return '<span class="badge badge-success">In Stock (' . $row->stock_quantity . ')</span>';
                        } elseif ($row->stock_quantity > 0) {
                            return '<span class="badge badge-warning">Low Stock (' . $row->stock_quantity . ')</span>';
                        } else {
                            return '<span class="badge badge-danger">Out of Stock</span>';
                        }
                    })
                    ->addColumn('actions', function($row) {
                        return Helper::getActionButtons($row->id, 'products', ['show', 'edit', 'delete']);
                    })
                    ->editColumn('created_at', function($row) {
                        return $row->created_at->format('M d, Y H:i');
                    })
                    ->filterColumn('name', function($query, $keyword) {
                        $query->where('name', 'LIKE', "%{$keyword}%");
                    })
                    ->filterColumn('sku', function($query, $keyword) {
                        $query->where('sku', 'LIKE', "%{$keyword}%");
                    })
                    ->rawColumns(['status_badge', 'featured_badge', 'stock_status', 'actions'])
                    ->make(true);
            }, 'products_datatable');
        }

        // Cache the view data for better performance
        $viewData = Cache::remember('products_view_data', 300, function () {
            return [
                'total_products' => Product::count(),
                'active_products' => Product::where('status', 'active')->count(),
                'featured_products' => Product::where('is_featured', true)->count(),
                'out_of_stock' => Product::where('stock_quantity', 0)->count(),
            ];
        });

        // Get categories for the modal
        $categories = ProductCategory::where('status', 'active')->pluck('category', 'id');
        $viewData['categories'] = $categories;

        return view('Product.index', $viewData);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = ProductCategory::where('status', 'active')->pluck('category', 'id');
        return view('Product.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        return DatabaseHelper::monitoredQuery(function () use ($request) {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'slug' => 'nullable|string|max:255|unique:products,slug,' . $request->id,
                'description' => 'nullable|string',
                'short_description' => 'nullable|string|max:500',
                'amount' => 'required|numeric|min:0',
                'category_id' => 'nullable|exists:product_categories,id',
                'sku' => 'nullable|string|max:100|unique:products,sku,' . $request->id,
                'status' => 'required|string|in:active,inactive,draft,archived',
                'weight' => 'nullable|numeric|min:0',
                'stock_quantity' => 'required|integer|min:0',
                'cost_price' => 'nullable|numeric|min:0',
                'track_inventory' => 'boolean',
                'is_featured' => 'boolean',
            ]);

            // Generate slug if not provided
            if (empty($validated['slug'])) {
                $validated['slug'] = Str::slug($validated['name']);
            }

            // Generate SKU if not provided
            if (empty($validated['sku'])) {
                $validated['sku'] = 'PRD-' . strtoupper(Str::random(8));
            }

            $product = Product::updateOrCreate(
                ['id' => $request->id],
                $validated
            );

            // Clear related cache
            Cache::forget('products_view_data');

            return Helper::successResponse('Product saved successfully', $product);
        }, 'product_store');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        return DatabaseHelper::monitoredQuery(function () use ($id) {
            $product = Cache::remember("product_show_{$id}", 300, function () use ($id) {
                return Product::with('category')->findOrFail($id);
            });

            return view('Product.show', compact('product'));
        }, 'product_show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        return DatabaseHelper::monitoredQuery(function () use ($id) {
            // Use cache for frequently accessed products
            $product = Cache::remember("product_{$id}", 300, function () use ($id) {
                return Product::findOrFail($id);
            });

            return Helper::successResponse('Product fetched', $product);
        }, 'product_edit');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        return DatabaseHelper::monitoredQuery(function () use ($request, $id) {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'slug' => 'nullable|string|max:255|unique:products,slug,' . $id,
                'description' => 'nullable|string',
                'short_description' => 'nullable|string|max:500',
                'amount' => 'required|numeric|min:0',
                'category_id' => 'nullable|exists:product_categories,id',
                'sku' => 'nullable|string|max:100|unique:products,sku,' . $id,
                'status' => 'required|string|in:active,inactive,draft,archived',
                'weight' => 'nullable|numeric|min:0',
                'stock_quantity' => 'required|integer|min:0',
                'cost_price' => 'nullable|numeric|min:0',
                'track_inventory' => 'boolean',
                'is_featured' => 'boolean',
            ]);

            $product = Product::findOrFail($id);
            $product->update($validated);

            // Clear related cache
            Cache::forget("product_{$id}");
            Cache::forget("product_show_{$id}");
            Cache::forget('products_view_data');

            return Helper::successResponse('Product updated', $product);
        }, 'product_update');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        return DatabaseHelper::monitoredQuery(function () use ($id) {
            $product = Product::findOrFail($id);
            $product->delete();

            // Clear related cache
            Cache::forget("product_{$id}");
            Cache::forget("product_show_{$id}");
            Cache::forget('products_view_data');

            return Helper::successResponse('Product deleted');
        }, 'product_destroy');
    }
}
