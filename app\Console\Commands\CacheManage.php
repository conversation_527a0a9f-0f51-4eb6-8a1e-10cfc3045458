<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Helpers\CacheHelper;

class CacheManage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:manage 
                            {action : Action to perform (clear, stats, warm-up)}
                            {--tags=* : Cache tags to target}
                            {--pattern= : Pattern to match for invalidation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage application cache (clear, stats, warm-up)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');
        
        switch ($action) {
            case 'clear':
                return $this->clearCache();
                
            case 'stats':
                return $this->showStats();
                
            case 'warm-up':
                return $this->warmUpCache();
                
            default:
                $this->error("Unknown action: {$action}");
                $this->info('Available actions: clear, stats, warm-up');
                return 1;
        }
    }

    /**
     * Clear cache
     */
    private function clearCache()
    {
        $tags = $this->option('tags');
        $pattern = $this->option('pattern');
        
        $this->info('Clearing cache...');
        
        if ($pattern) {
            $result = CacheHelper::invalidatePattern($pattern);
            $message = $result ? "Cache cleared for pattern: {$pattern}" : 'Failed to clear cache';
        } else {
            $result = CacheHelper::clearAll($tags);
            $message = $result ? 'Cache cleared successfully' : 'Failed to clear cache';
            
            if (!empty($tags)) {
                $message .= ' for tags: ' . implode(', ', $tags);
            }
        }
        
        if ($result) {
            $this->info($message);
            return 0;
        } else {
            $this->error($message);
            return 1;
        }
    }

    /**
     * Show cache statistics
     */
    private function showStats()
    {
        $this->info('Cache Statistics:');
        $stats = CacheHelper::getStats();
        
        if (isset($stats['error'])) {
            $this->error('Error getting cache stats: ' . $stats['error']);
            return 1;
        }
        
        $tableData = [];
        foreach ($stats as $key => $value) {
            if (is_array($value)) {
                $value = json_encode($value);
            }
            $tableData[] = [ucwords(str_replace('_', ' ', $key)), $value];
        }
        
        $this->table(['Metric', 'Value'], $tableData);
        
        // Show cache health
        $this->showCacheHealth($stats);
        
        return 0;
    }

    /**
     * Warm up cache
     */
    private function warmUpCache()
    {
        $this->info('Warming up cache...');
        
        $results = CacheHelper::warmUp();
        
        foreach ($results as $type => $result) {
            if ($type === 'error') {
                $this->error("Error: {$result}");
            } else {
                $status = $result ? '✓' : '✗';
                $this->line("{$status} {$type}: " . ($result ? 'Success' : 'Failed'));
            }
        }
        
        $this->info('Cache warm-up completed!');
        return 0;
    }

    /**
     * Show cache health information
     */
    private function showCacheHealth(array $stats)
    {
        $this->newLine();
        $this->info('Cache Health:');
        
        $health = [];
        
        // Check if cache is enabled
        if (!$stats['cache_enabled']) {
            $health[] = ['❌', 'Cache is disabled (using array driver)'];
        } else {
            $health[] = ['✅', 'Cache is enabled'];
        }
        
        // Check hit ratio (if available)
        if (isset($stats['hit_ratio'])) {
            $hitRatio = $stats['hit_ratio'];
            if ($hitRatio >= 80) {
                $health[] = ['✅', "Good hit ratio: {$hitRatio}%"];
            } elseif ($hitRatio >= 60) {
                $health[] = ['⚠️', "Moderate hit ratio: {$hitRatio}%"];
            } else {
                $health[] = ['❌', "Low hit ratio: {$hitRatio}%"];
            }
        }
        
        // Check cache size (if available)
        if (isset($stats['cache_keys'])) {
            $keys = $stats['cache_keys'];
            if ($keys > 10000) {
                $health[] = ['⚠️', "High number of cache keys: {$keys}"];
            } elseif ($keys > 0) {
                $health[] = ['✅', "Cache keys: {$keys}"];
            } else {
                $health[] = ['⚠️', 'No cache keys found'];
            }
        }
        
        // Check driver-specific health
        $driver = $stats['driver'];
        switch ($driver) {
            case 'redis':
                if (isset($stats['connected_clients'])) {
                    $clients = $stats['connected_clients'];
                    if ($clients > 100) {
                        $health[] = ['⚠️', "High Redis connections: {$clients}"];
                    } else {
                        $health[] = ['✅', "Redis connections: {$clients}"];
                    }
                }
                break;
                
            case 'file':
                if (isset($stats['cache_path'])) {
                    $path = $stats['cache_path'];
                    if (is_writable($path)) {
                        $health[] = ['✅', 'Cache directory is writable'];
                    } else {
                        $health[] = ['❌', 'Cache directory is not writable'];
                    }
                }
                break;
        }
        
        $this->table(['Status', 'Message'], $health);
        
        // Recommendations
        $this->showRecommendations($stats);
    }

    /**
     * Show cache recommendations
     */
    private function showRecommendations(array $stats)
    {
        $recommendations = [];
        
        if (!$stats['cache_enabled']) {
            $recommendations[] = 'Consider using Redis or Memcached for better performance';
        }
        
        if (isset($stats['hit_ratio']) && $stats['hit_ratio'] < 60) {
            $recommendations[] = 'Low hit ratio suggests cache keys may be expiring too quickly';
        }
        
        if ($stats['driver'] === 'file' && app()->environment('production')) {
            $recommendations[] = 'Consider using Redis for better performance in production';
        }
        
        if (isset($stats['cache_keys']) && $stats['cache_keys'] > 10000) {
            $recommendations[] = 'High number of cache keys - consider implementing cache cleanup';
        }
        
        if (!empty($recommendations)) {
            $this->newLine();
            $this->info('Recommendations:');
            foreach ($recommendations as $recommendation) {
                $this->line("• {$recommendation}");
            }
        }
    }
}
