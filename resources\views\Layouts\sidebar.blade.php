@php
use App\Helpers\SidebarHelper;
@endphp

<div id="kt_app_sidebar" class="app-sidebar flex-column" data-kt-drawer="true" data-kt-drawer-name="app-sidebar" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    {{-- Sidebar Logo --}}
    <div class="px-6 app-sidebar-logo" id="kt_app_sidebar_logo">
        <a href="{{route('admin-dashboard')}}" class="d-flex align-items-center text-decoration-none">
            <div class="d-flex align-items-center">
                <img alt="ArcLok Admin Logo" src="{{ asset('media/logos/WHITE LOGO.png') }}" class="h-200px w-16rem app-sidebar-logo-default" loading="lazy" />
                {{-- <span class="text-white fs-2 font-weight-bold ms-3">ArcLok Admin</span> --}}
            </div>
        </a>
        {{-- Sidebar toggle --}}
        <div id="kt_app_sidebar_toggle" class="app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary body-bg h-30px w-30px position-absolute top-50 start-100 translate-middle rotate" data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body" data-kt-toggle-name="app-sidebar-minimize" title="Toggle Sidebar">
            <span class="svg-icon svg-icon-2 rotate-180" aria-hidden="true">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.5" d="M14.2657 11.4343L18.45 7.25C18.8642 6.83579 18.8642 6.16421 18.45 5.75C18.0358 5.33579 17.3642 5.33579 16.95 5.75L11.4343 11.2657C11.0468 11.6532 11.0468 12.3468 11.4343 12.7343L16.95 18.25C17.3642 18.6642 18.0358 18.6642 18.45 18.25C18.8642 17.8358 18.8642 17.1642 18.45 16.75L14.2657 12.5657C13.9533 12.2533 13.9533 11.7467 14.2657 11.4343Z" fill="currentColor" />
                    <path d="M8.2657 11.4343L12.45 7.25C12.8642 6.83579 12.8642 6.16421 12.45 5.75C12.0358 5.33579 11.3642 5.33579 10.95 5.75L5.43431 11.2657C5.04678 11.6532 5.04678 12.3468 5.43431 12.7343L10.95 18.25C11.3642 18.6642 12.0358 18.6642 12.45 18.25C12.8642 17.8358 12.8642 17.1642 12.45 16.75L8.2657 12.5657C7.95328 12.2533 7.95328 11.7467 8.2657 11.4343Z" fill="currentColor" />
                </svg>
            </span>
        </div>
    </div>

    {{-- Sidebar menu --}}
    <div class="overflow-hidden app-sidebar-menu flex-column-fluid">
        <div id="kt_app_sidebar_menu_wrapper" class="my-5 app-sidebar-wrapper" data-kt-scroll="true" data-kt-scroll-activate="true" data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer" data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px" data-kt-scroll-save-state="true">
            <div class="menu menu-column menu-rounded menu-sub-indention px-3" id="kt_app_sidebar_menu" data-kt-menu="true" data-kt-menu-expand="false" role="navigation" aria-label="Main Navigation">

                {{-- Dashboard Link --}}
                <div class="menu-item">
                    <a class="menu-link {{ SidebarHelper::getActiveClass('admin-dashboard', 'admin-dashboard') }}"
                       href="{{ route('admin-dashboard') }}"
                       {{ SidebarHelper::isActive('admin-dashboard', 'admin-dashboard') ? 'aria-current=page' : '' }}>
                        <span class="menu-icon" aria-hidden="true">
                            <span class="svg-icon svg-icon-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="2" y="2" width="9" height="9" rx="2" fill="currentColor" />
                                    <rect opacity="0.3" x="13" y="2" width="9" height="9" rx="2" fill="currentColor" />
                                    <rect opacity="0.3" x="13" y="13" width="9" height="9" rx="2" fill="currentColor" />
                                    <rect opacity="0.3" x="2" y="13" width="9" height="9" rx="2" fill="currentColor" />
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Dashboard</span>
                    </a>
                </div>

                {{-- Manage Product Dropdown --}}
                @php
                    $productChildRoutes = ['products.index', 'products.create', 'products.edit', 'products.show', 'product-categories.index', 'product-categories.create', 'product-categories.edit', 'product-categories.show'];
                    $productChildSegments = ['product-categories', 'products'];
                    $hasActiveChild = SidebarHelper::shouldExpand($productChildRoutes, $productChildSegments);
                @endphp
                <div class="menu-item menu-accordion {{ $hasActiveChild ? 'show has-active-child' : '' }}" data-kt-menu-trigger="click">
                    <span class="menu-link {{ $hasActiveChild ? 'active' : '' }}">
                        <span class="menu-icon" aria-hidden="true">
                            <i class="fa fa-boxes"></i>
                        </span>
                        <span class="menu-title">Manage Product</span>
                        <span class="menu-arrow"></span>
                    </span>
                    <div class="menu-sub menu-sub-accordion {{ $hasActiveChild ? 'show' : '' }}" {{ $hasActiveChild ? 'style=display:block;' : '' }}>
                        <div class="menu-item">
                            <a class="menu-link {{ SidebarHelper::getActiveClass(['products.index', 'products.create', 'products.edit', 'products.show'], 'products') }}"
                               href="{{ route('products.index') }}"
                               {{ SidebarHelper::isActive(['products.index', 'products.create', 'products.edit', 'products.show'], 'products') ? 'aria-current=page' : '' }}>
                                <span class="menu-bullet" aria-hidden="true">
                                    <span class="bullet bullet-dot"></span>
                                </span>
                                <span class="menu-title">Products</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link {{ SidebarHelper::getActiveClass(['product-categories.index', 'product-categories.create', 'product-categories.edit', 'product-categories.show'], 'product-categories') }}"
                               href="{{ route('product-categories.index') }}"
                               {{ SidebarHelper::isActive(['product-categories.index', 'product-categories.create', 'product-categories.edit', 'product-categories.show'], 'product-categories') ? 'aria-current=page' : '' }}>
                                <span class="menu-bullet" aria-hidden="true">
                                    <span class="bullet bullet-dot"></span>
                                </span>
                                <span class="menu-title">Category</span>
                            </a>
                        </div>
                    </div>
                </div>
                {{-- End Manage Product Dropdown --}}

            </div>
        </div>
    </div>

    {{-- Credit Section --}}
    <div id="kt_app_sidebar_footer" class="app-sidebar-footer px-6 py-3 d-flex flex-column justify-content-center position-sticky" style="bottom: 0; background: rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); border-top: 1px solid rgba(255, 255, 255, 0.1);">
        <div class="text-center">
            <span class="text-muted fs-7 fw-semibold">Design and Developed By</span>
            <div class="text-white fs-6 fw-bold mt-1">Abhishek William</div>
        </div>
    </div>
    {{-- Credit Section --}}
</div>