<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Route;

class SidebarHelper
{
    /**
     * Check if a menu item is active based on route name or URL segments
     *
     * @param string|array $routes Route name(s) or URL pattern(s)
     * @param string|array $segments URL segment(s) to match
     * @param bool $exact Whether to match exactly or partially
     * @return bool
     */
    public static function isActive($routes = null, $segments = null, $exact = false)
    {
        // Check route names
        if ($routes) {
            $routes = is_array($routes) ? $routes : [$routes];
            foreach ($routes as $route) {
                if (Route::currentRouteName() === $route) {
                    return true;
                }
                // Check for wildcard route matching
                if (!$exact && str_contains($route, '*')) {
                    $pattern = str_replace('*', '.*', $route);
                    if (preg_match('/^' . $pattern . '$/', Route::currentRouteName())) {
                        return true;
                    }
                }
            }
        }

        // Check URL segments
        if ($segments) {
            $segments = is_array($segments) ? $segments : [$segments];
            $currentSegments = Request::segments();
            
            foreach ($segments as $segment) {
                if ($exact) {
                    // Exact match - all segments must match
                    if (implode('/', $currentSegments) === $segment) {
                        return true;
                    }
                } else {
                    // Partial match - check if segment exists in current URL
                    if (in_array($segment, $currentSegments)) {
                        return true;
                    }
                    // Check for pattern matching
                    if (str_contains($segment, '*')) {
                        $pattern = str_replace('*', '.*', $segment);
                        foreach ($currentSegments as $currentSegment) {
                            if (preg_match('/^' . $pattern . '$/', $currentSegment)) {
                                return true;
                            }
                        }
                    }
                }
            }
        }

        return false;
    }

    /**
     * Check if a parent menu should be expanded (has active children)
     *
     * @param array $childRoutes Array of child route names
     * @param array $childSegments Array of child URL segments
     * @return bool
     */
    public static function shouldExpand($childRoutes = [], $childSegments = [])
    {
        // Check if any child route is active
        foreach ($childRoutes as $route) {
            if (self::isActive($route)) {
                return true;
            }
        }

        // Check if any child segment is active
        foreach ($childSegments as $segment) {
            if (self::isActive(null, $segment)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get active CSS class for menu items
     *
     * @param string|array $routes Route name(s) or URL pattern(s)
     * @param string|array $segments URL segment(s) to match
     * @param bool $exact Whether to match exactly or partially
     * @param string $activeClass CSS class to apply when active
     * @return string
     */
    public static function getActiveClass($routes = null, $segments = null, $exact = false, $activeClass = 'active')
    {
        return self::isActive($routes, $segments, $exact) ? $activeClass : '';
    }

    /**
     * Get expanded CSS class for parent menu items
     *
     * @param array $childRoutes Array of child route names
     * @param array $childSegments Array of child URL segments
     * @param string $expandedClass CSS class to apply when expanded
     * @return string
     */
    public static function getExpandedClass($childRoutes = [], $childSegments = [], $expandedClass = 'show')
    {
        return self::shouldExpand($childRoutes, $childSegments) ? $expandedClass : '';
    }

    /**
     * Get menu item attributes for active state
     *
     * @param string|array $routes Route name(s) or URL pattern(s)
     * @param string|array $segments URL segment(s) to match
     * @param bool $exact Whether to match exactly or partially
     * @return array
     */
    public static function getMenuAttributes($routes = null, $segments = null, $exact = false)
    {
        $isActive = self::isActive($routes, $segments, $exact);
        
        return [
            'class' => $isActive ? 'menu-link active' : 'menu-link',
            'aria-current' => $isActive ? 'page' : null,
        ];
    }

    /**
     * Get parent menu attributes for expanded state
     *
     * @param array $childRoutes Array of child route names
     * @param array $childSegments Array of child URL segments
     * @return array
     */
    public static function getParentMenuAttributes($childRoutes = [], $childSegments = [])
    {
        $shouldExpand = self::shouldExpand($childRoutes, $childSegments);
        
        return [
            'class' => $shouldExpand ? 'menu-item menu-accordion show' : 'menu-item menu-accordion',
            'data-kt-menu-trigger' => 'click',
        ];
    }

    /**
     * Get submenu attributes for expanded state
     *
     * @param array $childRoutes Array of child route names
     * @param array $childSegments Array of child URL segments
     * @return array
     */
    public static function getSubmenuAttributes($childRoutes = [], $childSegments = [])
    {
        $shouldExpand = self::shouldExpand($childRoutes, $childSegments);
        
        return [
            'class' => $shouldExpand ? 'menu-sub menu-sub-accordion show' : 'menu-sub menu-sub-accordion',
            'style' => $shouldExpand ? 'display: block;' : null,
        ];
    }

    /**
     * Get current route segments as array
     *
     * @return array
     */
    public static function getCurrentSegments()
    {
        return Request::segments();
    }

    /**
     * Get current route name
     *
     * @return string|null
     */
    public static function getCurrentRoute()
    {
        return Route::currentRouteName();
    }

    /**
     * Check if current URL matches a pattern
     *
     * @param string $pattern URL pattern with wildcards
     * @return bool
     */
    public static function matchesPattern($pattern)
    {
        $currentUrl = Request::path();
        $pattern = str_replace('*', '.*', $pattern);
        return preg_match('/^' . $pattern . '$/', $currentUrl);
    }

    /**
     * Generate breadcrumb data based on current route
     *
     * @return array
     */
    public static function getBreadcrumbs()
    {
        $segments = self::getCurrentSegments();
        $breadcrumbs = [];
        $url = '';

        // Add home/dashboard
        $breadcrumbs[] = [
            'title' => 'Dashboard',
            'url' => route('admin-dashboard'),
            'active' => empty($segments) || (count($segments) === 1 && $segments[0] === 'admin-dashboard')
        ];

        // Build breadcrumbs from segments
        foreach ($segments as $index => $segment) {
            $url .= '/' . $segment;
            $isLast = $index === count($segments) - 1;
            
            $breadcrumbs[] = [
                'title' => ucwords(str_replace(['-', '_'], ' ', $segment)),
                'url' => $isLast ? null : $url,
                'active' => $isLast
            ];
        }

        return $breadcrumbs;
    }

    /**
     * Get menu configuration array
     *
     * @return array
     */
    public static function getMenuConfig()
    {
        return [
            'dashboard' => [
                'title' => 'Dashboard',
                'icon' => 'dashboard',
                'route' => 'admin-dashboard',
                'segments' => ['admin-dashboard']
            ],
            'products' => [
                'title' => 'Manage Product',
                'icon' => 'fa fa-boxes',
                'children' => [
                    'products-list' => [
                        'title' => 'Products',
                        'route' => '#',
                        'segments' => ['products']
                    ],
                    'product-categories' => [
                        'title' => 'Category',
                        'route' => 'product-categories.index',
                        'segments' => ['product-categories']
                    ]
                ]
            ]
        ];
    }
}
