<?php use App\Helpers\Helper; ?>

<?php $__env->startSection('title', 'Product Details - ' . $product->name); ?>
<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0"><?php echo e($product->name); ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin-dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('products.index')); ?>">Products</a></li>
                    <li class="breadcrumb-item active"><?php echo e($product->name); ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?php echo e(route('products.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Products
            </a>
            <a href="<?php echo e(route('products.edit', $product->id)); ?>" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit Product
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Product Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Product Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Name:</td>
                                    <td><?php echo e($product->name); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">SKU:</td>
                                    <td><span class="badge badge-light-info"><?php echo e($product->sku); ?></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Slug:</td>
                                    <td><code><?php echo e($product->slug); ?></code></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Category:</td>
                                    <td>
                                        <?php if($product->category): ?>
                                            <span class="badge badge-light-success"><?php echo e($product->category->category); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">No Category</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Status:</td>
                                    <td><?php echo Helper::getStatusBadge($product->status === 'active' ? 1 : 0); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Selling Amount:</td>
                                    <td class="text-success fw-bold">$<?php echo e(number_format($product->amount, 2)); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Cost Price:</td>
                                    <td>
                                        <?php if($product->cost_price): ?>
                                            $<?php echo e(number_format($product->cost_price, 2)); ?>

                                        <?php else: ?>
                                            <span class="text-muted">Not set</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Stock Quantity:</td>
                                    <td>
                                        <?php if($product->stock_quantity > 10): ?>
                                            <span class="badge badge-success"><?php echo e($product->stock_quantity); ?> in stock</span>
                                        <?php elseif($product->stock_quantity > 0): ?>
                                            <span class="badge badge-warning"><?php echo e($product->stock_quantity); ?> low stock</span>
                                        <?php else: ?>
                                            <span class="badge badge-danger">Out of stock</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Weight:</td>
                                    <td>
                                        <?php if($product->weight): ?>
                                            <?php echo e($product->weight); ?> kg
                                        <?php else: ?>
                                            <span class="text-muted">Not set</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Dimensions:</td>
                                    <td>
                                        <?php if($product->length || $product->width || $product->height): ?>
                                            <?php echo e($product->length); ?>L x <?php echo e($product->width); ?>W x <?php echo e($product->height); ?>H
                                        <?php else: ?>
                                            <span class="text-muted">Not set</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if($product->short_description): ?>
                    <div class="mt-3">
                        <h6 class="fw-bold">Short Description:</h6>
                        <p class="text-muted"><?php echo e($product->short_description); ?></p>
                    </div>
                    <?php endif; ?>

                    <?php if($product->description): ?>
                    <div class="mt-3">
                        <h6 class="fw-bold">Description:</h6>
                        <div class="text-muted">
                            <?php echo nl2br(e($product->description)); ?>

                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Product Stats & Options -->
        <div class="col-lg-4">
            <!-- Quick Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Stats</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Featured Product:</span>
                        <?php if($product->is_featured): ?>
                            <span class="badge badge-warning">Yes</span>
                        <?php else: ?>
                            <span class="badge badge-light">No</span>
                        <?php endif; ?>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Track Inventory:</span>
                        <?php if($product->track_inventory): ?>
                            <span class="badge badge-success">Enabled</span>
                        <?php else: ?>
                            <span class="badge badge-secondary">Disabled</span>
                        <?php endif; ?>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Created:</span>
                        <span class="text-muted"><?php echo e($product->created_at->format('M d, Y')); ?></span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Last Updated:</span>
                        <span class="text-muted"><?php echo e($product->updated_at->format('M d, Y')); ?></span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('products.edit', $product->id)); ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Product
                        </a>
                        <button type="button" class="btn btn-danger delete-product" data-id="<?php echo e($product->id); ?>">
                            <i class="fas fa-trash"></i> Delete Product
                        </button>
                        <hr>
                        <a href="<?php echo e(route('products.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-list"></i> All Products
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Delete product
    $('.delete-product').on('click', function() {
        var id = $(this).data('id');

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                showLoading('Deleting product...');
                $.ajax({
                    url: '/products/' + id,
                    type: 'DELETE',
                    headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                    success: function(res) {
                        hideLoading();
                        if (res.success) {
                            showSuccess('Success', res.message);
                            setTimeout(function() {
                                window.location.href = '<?php echo e(route("products.index")); ?>';
                            }, 1500);
                        } else {
                            showError('Error', res.message || 'Failed to delete product');
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        showError('Error', xhr.responseJSON?.message || 'Failed to delete product');
                    }
                });
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\arclok_admin\resources\views/Product/show.blade.php ENDPATH**/ ?>