<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Helpers\SecurityHelper;

class AuthController extends Controller
{
    // ***************************Login-Page-Show-Function*******************************
    public function showLoginForm()
    {
        return view('login');
    }
    // ***************************Login-Page-Show-Function*******************************

    // ***************************Login-Logic-Function*******************************
    public function login(Request $request)
    {
        try {
            // Validate input
            $request->validate([
                'email' => 'required|email|max:255',
                'password' => 'required|string|min:6|max:255',
            ]);

            $email = $request->email;
            $identifier = $email . '|' . $request->ip();

            // Check rate limiting
            if (SecurityHelper::checkLoginRateLimit($identifier)) {
                SecurityHelper::logSecurityEvent('Login rate limit exceeded', [
                    'email' => $email,
                    'ip' => $request->ip()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Too many login attempts. Please try again later.'
                ], 429);
            }

            $credentials = $request->only('email', 'password');

            if (Auth::attempt($credentials, $request->boolean('remember'))) {
                // Clear failed login attempts
                SecurityHelper::clearLoginAttempts($identifier);

                // Log successful login
                SecurityHelper::logSecurityEvent('Successful login', [
                    'user_id' => Auth::id(),
                    'email' => $email
                ]);

                // Regenerate session ID for security
                $request->session()->regenerate();

                return response()->json([
                    'success' => true,
                    'message' => 'Login successful!',
                    'redirect' => route('admin-dashboard')
                ]);
            }

            // Record failed login attempt
            SecurityHelper::recordFailedLogin($identifier);

            return response()->json([
                'success' => false,
                'message' => 'Invalid email or password.'
            ], 401);

        } catch (Exception $e) {
            Log::error('Login Error: ' . $e->getMessage(), [
                'email' => $request->email ?? 'unknown',
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your request.'
            ], 500);
        }
    }
    // ***************************Login-Logic-Function*******************************

    // ***************************Dashboard-Page-Show-Function*******************************
    public function showDashboard()
    {
        return view('Web.dashboard');
    }
    // ***************************Dashboard-Page-Show-Function*******************************

    // ***************************Logout-Function*******************************
    public function logout(Request $request)
    {
        try {
            // Log logout event
            SecurityHelper::logSecurityEvent('User logout', [
                'user_id' => Auth::id()
            ]);

            // Logout user
            Auth::logout();

            // Invalidate session
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('showloginform')
                ->with('success', 'You have been logged out successfully.');

        } catch (Exception $e) {
            Log::error('Logout Error: ' . $e->getMessage());
            return redirect()->route('showloginform');
        }
    }
    // ***************************Logout-Function*******************************
}
