<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;

class SecurityHelper
{
    /**
     * Security constants
     */
    const MAX_LOGIN_ATTEMPTS = 5;
    const LOGIN_LOCKOUT_MINUTES = 15;
    const PASSWORD_MIN_LENGTH = 8;
    const SESSION_TIMEOUT_MINUTES = 120;

    /**
     * Validate and sanitize input data
     *
     * @param array $data
     * @param array $rules
     * @return array
     */
    public static function sanitizeInput(array $data, array $rules = []): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // Basic sanitization
                $value = trim($value);
                $value = strip_tags($value);
                $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                
                // Apply specific rules if provided
                if (isset($rules[$key])) {
                    $value = self::applyRule($value, $rules[$key]);
                }
            } elseif (is_array($value)) {
                $value = self::sanitizeInput($value, $rules[$key] ?? []);
            }
            
            $sanitized[$key] = $value;
        }

        return $sanitized;
    }

    /**
     * Apply specific sanitization rule
     *
     * @param string $value
     * @param string $rule
     * @return string
     */
    private static function applyRule(string $value, string $rule): string
    {
        switch ($rule) {
            case 'email':
                return filter_var($value, FILTER_SANITIZE_EMAIL);
            case 'url':
                return filter_var($value, FILTER_SANITIZE_URL);
            case 'int':
                return filter_var($value, FILTER_SANITIZE_NUMBER_INT);
            case 'float':
                return filter_var($value, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
            case 'alpha':
                return preg_replace('/[^a-zA-Z]/', '', $value);
            case 'alphanumeric':
                return preg_replace('/[^a-zA-Z0-9]/', '', $value);
            case 'slug':
                return Str::slug($value);
            default:
                return $value;
        }
    }

    /**
     * Generate secure password hash
     *
     * @param string $password
     * @return string
     */
    public static function hashPassword(string $password): string
    {
        return Hash::make($password, [
            'rounds' => 12, // Higher cost for better security
        ]);
    }

    /**
     * Verify password strength
     *
     * @param string $password
     * @return array
     */
    public static function validatePasswordStrength(string $password): array
    {
        $errors = [];
        
        if (strlen($password) < self::PASSWORD_MIN_LENGTH) {
            $errors[] = "Password must be at least " . self::PASSWORD_MIN_LENGTH . " characters long";
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = "Password must contain at least one uppercase letter";
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = "Password must contain at least one lowercase letter";
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = "Password must contain at least one number";
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "Password must contain at least one special character";
        }
        
        // Check for common passwords
        if (self::isCommonPassword($password)) {
            $errors[] = "Password is too common, please choose a more secure password";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'strength' => self::calculatePasswordStrength($password)
        ];
    }

    /**
     * Check if password is commonly used
     *
     * @param string $password
     * @return bool
     */
    private static function isCommonPassword(string $password): bool
    {
        $commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey'
        ];
        
        return in_array(strtolower($password), $commonPasswords);
    }

    /**
     * Calculate password strength score
     *
     * @param string $password
     * @return int
     */
    private static function calculatePasswordStrength(string $password): int
    {
        $score = 0;
        
        // Length bonus
        $score += min(strlen($password) * 2, 20);
        
        // Character variety bonus
        if (preg_match('/[a-z]/', $password)) $score += 5;
        if (preg_match('/[A-Z]/', $password)) $score += 5;
        if (preg_match('/[0-9]/', $password)) $score += 5;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 10;
        
        // Penalty for common patterns
        if (preg_match('/(.)\1{2,}/', $password)) $score -= 10; // Repeated characters
        if (preg_match('/123|abc|qwe/i', $password)) $score -= 5; // Sequential characters
        
        return max(0, min(100, $score));
    }

    /**
     * Implement rate limiting for login attempts
     *
     * @param string $identifier
     * @return bool
     */
    public static function checkLoginRateLimit(string $identifier): bool
    {
        $key = 'login_attempts:' . $identifier;
        
        return RateLimiter::tooManyAttempts($key, self::MAX_LOGIN_ATTEMPTS);
    }

    /**
     * Record failed login attempt
     *
     * @param string $identifier
     * @return void
     */
    public static function recordFailedLogin(string $identifier): void
    {
        $key = 'login_attempts:' . $identifier;
        
        RateLimiter::hit($key, self::LOGIN_LOCKOUT_MINUTES * 60);
        
        Log::warning('Failed login attempt', [
            'identifier' => $identifier,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Clear login attempts after successful login
     *
     * @param string $identifier
     * @return void
     */
    public static function clearLoginAttempts(string $identifier): void
    {
        $key = 'login_attempts:' . $identifier;
        RateLimiter::clear($key);
    }

    /**
     * Generate secure random token
     *
     * @param int $length
     * @return string
     */
    public static function generateSecureToken(int $length = 32): string
    {
        return Str::random($length);
    }

    /**
     * Encrypt sensitive data
     *
     * @param mixed $data
     * @return string
     */
    public static function encryptData($data): string
    {
        return Crypt::encrypt($data);
    }

    /**
     * Decrypt sensitive data
     *
     * @param string $encryptedData
     * @return mixed
     */
    public static function decryptData(string $encryptedData)
    {
        try {
            return Crypt::decrypt($encryptedData);
        } catch (\Exception $e) {
            Log::error('Decryption failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Validate CSRF token
     *
     * @param string $token
     * @return bool
     */
    public static function validateCsrfToken(string $token): bool
    {
        return hash_equals(session()->token(), $token);
    }

    /**
     * Check for suspicious activity
     *
     * @param array $context
     * @return bool
     */
    public static function detectSuspiciousActivity(array $context = []): bool
    {
        $suspicious = false;
        $reasons = [];

        // Check for rapid requests from same IP
        $ip = request()->ip();
        $requestKey = 'requests:' . $ip;
        
        if (RateLimiter::tooManyAttempts($requestKey, 100)) { // 100 requests per minute
            $suspicious = true;
            $reasons[] = 'Too many requests from IP';
        }
        
        RateLimiter::hit($requestKey, 60); // 1 minute window

        // Check for suspicious user agents
        $userAgent = request()->userAgent();
        $suspiciousAgents = ['bot', 'crawler', 'spider', 'scraper'];
        
        foreach ($suspiciousAgents as $agent) {
            if (stripos($userAgent, $agent) !== false) {
                $suspicious = true;
                $reasons[] = 'Suspicious user agent';
                break;
            }
        }

        // Check for SQL injection patterns
        $input = json_encode(request()->all());
        $sqlPatterns = [
            '/union\s+select/i',
            '/drop\s+table/i',
            '/insert\s+into/i',
            '/delete\s+from/i',
            '/update\s+set/i'
        ];
        
        foreach ($sqlPatterns as $pattern) {
            if (preg_match($pattern, $input)) {
                $suspicious = true;
                $reasons[] = 'SQL injection attempt detected';
                break;
            }
        }

        // Check for XSS patterns
        $xssPatterns = [
            '/<script/i',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<iframe/i'
        ];
        
        foreach ($xssPatterns as $pattern) {
            if (preg_match($pattern, $input)) {
                $suspicious = true;
                $reasons[] = 'XSS attempt detected';
                break;
            }
        }

        if ($suspicious) {
            Log::warning('Suspicious activity detected', [
                'ip' => $ip,
                'user_agent' => $userAgent,
                'reasons' => $reasons,
                'context' => $context,
                'request_data' => request()->all(),
                'timestamp' => now()->toISOString()
            ]);
        }

        return $suspicious;
    }

    /**
     * Generate Content Security Policy header
     *
     * @return string
     */
    public static function generateCSPHeader(): string
    {
        $directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://code.jquery.com",
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
            "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com",
            "img-src 'self' data: https:",
            "connect-src 'self'",
            "frame-ancestors 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ];

        return implode('; ', $directives);
    }

    /**
     * Log security event
     *
     * @param string $event
     * @param array $context
     * @return void
     */
    public static function logSecurityEvent(string $event, array $context = []): void
    {
        Log::channel('security')->info($event, array_merge($context, [
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString()
        ]));
    }

    /**
     * Check session security
     *
     * @return bool
     */
    public static function validateSession(): bool
    {
        // Check session timeout
        $lastActivity = session('last_activity', 0);
        $timeout = self::SESSION_TIMEOUT_MINUTES * 60;
        
        if (time() - $lastActivity > $timeout) {
            session()->flush();
            return false;
        }

        // Update last activity
        session(['last_activity' => time()]);

        // Check for session hijacking
        $currentFingerprint = self::generateSessionFingerprint();
        $storedFingerprint = session('fingerprint');
        
        if ($storedFingerprint && $currentFingerprint !== $storedFingerprint) {
            session()->flush();
            self::logSecurityEvent('Session hijacking attempt detected');
            return false;
        }

        if (!$storedFingerprint) {
            session(['fingerprint' => $currentFingerprint]);
        }

        return true;
    }

    /**
     * Generate session fingerprint
     *
     * @return string
     */
    private static function generateSessionFingerprint(): string
    {
        return hash('sha256', 
            request()->ip() . 
            request()->userAgent() . 
            request()->header('Accept-Language', '')
        );
    }
}
