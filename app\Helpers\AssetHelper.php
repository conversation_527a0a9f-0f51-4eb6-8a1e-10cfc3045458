<?php

namespace App\Helpers;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;

class AssetHelper
{
    /**
     * Get versioned asset URL with cache busting
     *
     * @param string $path
     * @return string
     */
    public static function asset($path)
    {
        $fullPath = public_path($path);
        
        if (File::exists($fullPath)) {
            $version = Cache::remember("asset_version_{$path}", 3600, function () use ($fullPath) {
                return filemtime($fullPath);
            });
            
            return asset($path) . '?v=' . $version;
        }
        
        return asset($path);
    }

    /**
     * Combine and minify CSS files
     *
     * @param array $files
     * @param string $outputName
     * @return string
     */
    public static function combineCss(array $files, $outputName = 'combined.css')
    {
        $cacheKey = 'combined_css_' . md5(implode('|', $files));
        
        return Cache::remember($cacheKey, 3600, function () use ($files, $outputName) {
            $combinedContent = '';
            $lastModified = 0;
            
            foreach ($files as $file) {
                $filePath = public_path($file);
                if (File::exists($filePath)) {
                    $content = File::get($filePath);
                    $combinedContent .= self::minifyCss($content) . "\n";
                    $lastModified = max($lastModified, filemtime($filePath));
                }
            }
            
            $outputPath = public_path("css/combined/{$outputName}");
            File::ensureDirectoryExists(dirname($outputPath));
            File::put($outputPath, $combinedContent);
            
            return asset("css/combined/{$outputName}") . '?v=' . $lastModified;
        });
    }

    /**
     * Combine and minify JavaScript files
     *
     * @param array $files
     * @param string $outputName
     * @return string
     */
    public static function combineJs(array $files, $outputName = 'combined.js')
    {
        $cacheKey = 'combined_js_' . md5(implode('|', $files));
        
        return Cache::remember($cacheKey, 3600, function () use ($files, $outputName) {
            $combinedContent = '';
            $lastModified = 0;
            
            foreach ($files as $file) {
                $filePath = public_path($file);
                if (File::exists($filePath)) {
                    $content = File::get($filePath);
                    $combinedContent .= self::minifyJs($content) . ";\n";
                    $lastModified = max($lastModified, filemtime($filePath));
                }
            }
            
            $outputPath = public_path("js/combined/{$outputName}");
            File::ensureDirectoryExists(dirname($outputPath));
            File::put($outputPath, $combinedContent);
            
            return asset("js/combined/{$outputName}") . '?v=' . $lastModified;
        });
    }

    /**
     * Basic CSS minification
     *
     * @param string $css
     * @return string
     */
    private static function minifyCss($css)
    {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Remove unnecessary whitespace
        $css = str_replace(["\r\n", "\r", "\n", "\t"], '', $css);
        $css = preg_replace('/\s+/', ' ', $css);
        
        // Remove space around specific characters
        $css = str_replace([' {', '{ ', ' }', '} ', ': ', ' :', '; ', ' ;', ', ', ' ,'], 
                          ['{', '{', '}', '}', ':', ':', ';', ';', ',', ','], $css);
        
        return trim($css);
    }

    /**
     * Basic JavaScript minification
     *
     * @param string $js
     * @return string
     */
    private static function minifyJs($js)
    {
        // Remove single-line comments (but preserve URLs)
        $js = preg_replace('/(?<!:)\/\/.*$/m', '', $js);
        
        // Remove multi-line comments
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
        
        // Remove unnecessary whitespace
        $js = preg_replace('/\s+/', ' ', $js);
        
        // Remove space around operators and punctuation
        $js = str_replace([' = ', ' + ', ' - ', ' * ', ' / ', ' == ', ' != ', ' === ', ' !== ',
                          ' < ', ' > ', ' <= ', ' >= ', ' && ', ' || ', ' { ', ' } ', ' ( ', ' ) ',
                          ' [ ', ' ] ', ' ; ', ' , '], 
                         ['=', '+', '-', '*', '/', '==', '!=', '===', '!==',
                          '<', '>', '<=', '>=', '&&', '||', '{', '}', '(', ')',
                          '[', ']', ';', ','], $js);
        
        return trim($js);
    }

    /**
     * Generate critical CSS for above-the-fold content
     *
     * @param array $criticalSelectors
     * @return string
     */
    public static function generateCriticalCss(array $criticalSelectors = [])
    {
        $defaultSelectors = [
            'body', 'html', '.app-root', '.app-page', '.app-sidebar', '.app-header',
            '.menu', '.menu-item', '.menu-link', '.active', '.show'
        ];
        
        $selectors = array_merge($defaultSelectors, $criticalSelectors);
        $criticalCss = '';
        
        // Extract critical CSS from main stylesheets
        $stylesheets = [
            public_path('css/style.bundle.css'),
            public_path('css/sidebar-active.css')
        ];
        
        foreach ($stylesheets as $stylesheet) {
            if (File::exists($stylesheet)) {
                $css = File::get($stylesheet);
                foreach ($selectors as $selector) {
                    $pattern = '/(' . preg_quote($selector, '/') . '[^{]*\{[^}]*\})/';
                    if (preg_match_all($pattern, $css, $matches)) {
                        $criticalCss .= implode("\n", $matches[1]) . "\n";
                    }
                }
            }
        }
        
        return self::minifyCss($criticalCss);
    }

    /**
     * Preload critical resources
     *
     * @param array $resources
     * @return string
     */
    public static function preloadResources(array $resources = [])
    {
        $defaultResources = [
            ['href' => asset('css/style.bundle.css'), 'as' => 'style'],
            ['href' => asset('js/scripts.bundle.js'), 'as' => 'script'],
            ['href' => asset('css/sidebar-active.css'), 'as' => 'style']
        ];
        
        $resources = array_merge($defaultResources, $resources);
        $preloadTags = '';
        
        foreach ($resources as $resource) {
            $preloadTags .= sprintf(
                '<link rel="preload" href="%s" as="%s"%s>' . "\n",
                $resource['href'],
                $resource['as'],
                isset($resource['crossorigin']) ? ' crossorigin' : ''
            );
        }
        
        return $preloadTags;
    }

    /**
     * Generate service worker for caching
     *
     * @param array $cacheFiles
     * @return void
     */
    public static function generateServiceWorker(array $cacheFiles = [])
    {
        $defaultCacheFiles = [
            '/css/style.bundle.css',
            '/css/sidebar-active.css',
            '/js/scripts.bundle.js',
            '/js/sidebar-enhanced.js',
            '/js/helpers.js'
        ];
        
        $cacheFiles = array_merge($defaultCacheFiles, $cacheFiles);
        
        $serviceWorkerContent = "
const CACHE_NAME = 'arclok-admin-v1';
const urlsToCache = " . json_encode($cacheFiles) . ";

self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                return cache.addAll(urlsToCache);
            })
    );
});

self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                if (response) {
                    return response;
                }
                return fetch(event.request);
            }
        )
    );
});
";
        
        File::put(public_path('sw.js'), $serviceWorkerContent);
    }

    /**
     * Get optimized font loading CSS
     *
     * @return string
     */
    public static function getOptimizedFontCss()
    {
        return "
        @font-face {
            font-family: 'Inter';
            font-style: normal;
            font-weight: 300 700;
            font-display: swap;
            src: url('https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700&display=swap');
        }
        
        /* Fallback fonts */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        ";
    }

    /**
     * Clear asset cache
     *
     * @return void
     */
    public static function clearCache()
    {
        Cache::forget('combined_css_*');
        Cache::forget('combined_js_*');
        Cache::forget('asset_version_*');
        
        // Clear combined files
        $combinedDirs = [
            public_path('css/combined'),
            public_path('js/combined')
        ];
        
        foreach ($combinedDirs as $dir) {
            if (File::exists($dir)) {
                File::deleteDirectory($dir);
            }
        }
    }

    /**
     * Get asset loading strategy based on environment
     *
     * @return array
     */
    public static function getLoadingStrategy()
    {
        $isProduction = app()->environment('production');
        
        return [
            'combine_files' => $isProduction,
            'minify' => $isProduction,
            'use_cdn' => $isProduction,
            'enable_gzip' => $isProduction,
            'cache_duration' => $isProduction ? 31536000 : 0, // 1 year in production
        ];
    }
}
