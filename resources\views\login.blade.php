<!DOCTYPE html>
<html lang="en">
	<head>
		<title>Admin Panel | A full-service admin panel</title>
		<meta charset="utf-8" />
		<link rel="shortcut icon" href="{{ asset('media/logos/logo_white.png') }}"/>
		<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
		<link href="{{ asset('plugins/global/plugins.bundle.css') }}" rel="stylesheet" type="text/css" />
		<link href="{{ asset('css/style.bundle.css') }}" rel="stylesheet" type="text/css" />
	<style>
		body, html {
			height: 100%;
			margin: 0;
			padding: 0;
		}
		.login-main-container {
			min-height: 100vh;
			display: flex;
			align-items: stretch;
			justify-content: center;
			background: #fff;
		}
		.login-left, .login-right {
			flex: 1 1 0%;
			display: flex;
			align-items: center;
			justify-content: center;
			min-width: 0;
			background: transparent;
			box-shadow: none;
		}
		.login-left {
			/* No background */
		}
		.login-right {
			flex-direction: column;
			padding: 0 32px;
		}
		.login-card {
			background: transparent;
			border-radius: 0;
			box-shadow: none;
			padding: 32px 28px 28px 28px;
			max-width: 410px;
			width: 100%;
			margin: 0 auto;
		}
		.login-logo {
			height: 120px;
			max-width: 100%;
			width: auto;
			margin-bottom: 12px;
		}
		.login-title {
			font-size: 2rem;
			font-weight: 700;
			margin-bottom: 8px;
		}
		.login-desc {
			color: #6c757d;
			font-size: 1rem;
			margin-bottom: 18px;
		}
		.form-control {
			border-radius: 8px !important;
			padding: 10px 14px !important;
			font-size: 1rem;
		}
		.btn-primary {
			border-radius: 8px;
			padding: 10px 0;
			font-size: 1.1rem;
			font-weight: 600;
		}
		@media (max-width: 991.98px) {
			.login-main-container {
				flex-direction: column;
				padding: 24px 0;
			}
			.login-left, .login-right {
				flex: unset;
				width: 100%;
				min-height: unset;
				padding: 0;
			}
			.login-card {
				padding: 24px 10px 18px 10px;
			}
			.login-logo {
				height: 80px;
			}
			.login-right {
				padding: 24px 10px;
			}
		}
	</style>
	</head>

	<body id="kt_body" class="app-blank app-blank">
		<div class="login-main-container">
			<div class="login-left">
				<div class="login-card">
					<div class="text-center">
						<img alt="Logo" src="{{ asset('media/logos/ArcLok new wordmark black.png') }}" class="login-logo" />
						<div class="login-title">Sign In</div>
						<div class="login-desc">Here is the admin panel login for <span class="fw-bold">ArcLok Admin</span></div>
					</div>
					<form class="form w-100" id="kt_sign_in_form" method="POST" action="{{ route('login') }}">
						@csrf
						<div class="mb-3">
							<input type="text" placeholder="Email" name="email" autocomplete="off" class="form-control bg-transparent" />
						</div>
						<div class="mb-3">
							<input type="password" placeholder="Password" name="password" autocomplete="off" class="form-control bg-transparent" />
						</div>
						<div class="d-flex flex-stack flex-wrap gap-3 fs-base fw-semibold mb-3 align-items-center">
							<div class="form-check">
								<input class="form-check-input" type="checkbox" name="remember" id="remember_me" />
								<label class="form-check-label text-gray-500" for="remember_me">Remember Me</label>
							</div>
							<a href="#" class="link-primary">Forgot Password?</a>
						</div>
						<button type="submit" class="btn btn-primary w-100 mb-2">Sign In</button>
						<div class="text-gray-500 text-center fw-semibold fs-6 mt-2">Health is the greatest gift, contentment the greatest wealth.</div>
					</form>
				</div>
			</div>
			<div class="login-right">
				<div class="text-center w-100">
					<img src="{{ asset('media/misc/user-login.jpg') }}" alt="Welcome" style="max-width: 340px; width: 100%; border-radius: 16px; box-shadow: 0 4px 24px 0 rgba(0,0,0,0.08); margin-bottom: 24px;" />
					<div class="text-dark fs-5 fw-semibold mb-2">Welcome to <span class="text-warning">ArcLok Admin</span></div>
					<div class="text-muted fs-6 mb-2" style="max-width: 400px; margin: 0 auto;">
						The powerful admin panel for managing your ecommerce platform specializing in T-shirts and clothing. Easily organize products, track inventory, manage orders, and oversee all aspects of your online apparel business.<br><br>
						ArcLok Admin helps streamline your workflow so you can focus on growing your brand and delivering the best shopping experience to your customers.
					</div>
				</div>
			</div>
		</div>
	</body>
</html>
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
	$(document).ready(function() {
		$('#kt_sign_in_form').on('submit', function(e) {
			e.preventDefault();

			$.ajax({
				headers: {
					'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
				},
				type: 'POST',
				url: $(this).attr('action'), // Now points to /login
				data: $(this).serialize(),
				success: function(response) {
					if (response.success) {
						Swal.fire({
							icon: 'success',
							title: 'Welcome!',
							text: response.message,
							timer: 2000,
							showConfirmButton: false
						}).then(() => {
							window.location.href = response.redirect;
						});
					} else {
						Swal.fire({
							icon: 'error',
							title: 'Oops...',
							text: response.message
						});
					}
				},
				error: function(xhr) {
					Swal.fire({
						icon: 'error',
						title: 'Login Failed',
						text: xhr.responseJSON?.message || 'Invalid credentials!'
					});
				}
			});
		});
	});
</script>
