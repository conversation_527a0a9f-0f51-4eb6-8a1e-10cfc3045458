
<?php $__env->startSection('title', 'Product Categories'); ?>
<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Product Categories</h4>
            <button class="btn btn-primary" id="addCategoryBtn">Add Category</button>
        </div>
        <div class="card-body">
            <table id="categoryTable" class="table table-bordered table-striped dt-responsive nowrap w-100">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Category</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
    </div>
</div>
<?php echo $__env->make('ProductCategory.modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>
<script src="/js/helpers.js"></script>
<script>
$(function() {
    var table = $('#categoryTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: '<?php echo e(route('product-categories.index')); ?>',
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'category', name: 'category' },
            { data: 'status_badge', name: 'status', orderable: false, searchable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false },
        ]
    });

    // Open modal for add
    $('#addCategoryBtn').on('click', function() {
        $('#categoryForm')[0].reset();
        $('#category_id').val('');
        $('#categoryModalLabel').text('Add Category');
        $('#categoryModal').modal('show');
    });

    // Edit category
    $('#categoryTable').on('click', '.btn-warning', function(e) {
        e.preventDefault();
        var id = $(this).closest('tr').find('button, a').data('id') || $(this).data('id');
        if (!id) return;
        showLoading('Fetching category...');
        $.get('/product-categories/' + id + '/edit', function(res) {
            hideLoading();
            if (res.success) {
                $('#category_id').val(res.data.id);
                $('#category').val(res.data.category);
                $('#status').val(res.data.status);
                $('#categoryModalLabel').text('Edit Category');
                $('#categoryModal').modal('show');
            } else {
                showError('Error', res.message || 'Failed to fetch category');
            }
        }).fail(function(xhr) {
            hideLoading();
            showError('Error', xhr.responseJSON?.message || 'Failed to fetch category');
        });
    });

    // Save (add/update) category
    $('#categoryForm').on('submit', function(e) {
        e.preventDefault();
        var id = $('#category_id').val();
        var url = id ? '/product-categories/' + id : '/product-categories';
        var method = id ? 'PUT' : 'POST';
        showLoading('Saving category...');
        $.ajax({
            url: url,
            type: method,
            data: $(this).serialize(),
            headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
            success: function(res) {
                hideLoading();
                if (res.success) {
                    $('#categoryModal').modal('hide');
                    showSuccess('Success', res.message);
                    table.ajax.reload(null, false);
                } else {
                    showError('Error', res.message || 'Failed to save category');
                }
            },
            error: function(xhr) {
                hideLoading();
                showError('Error', xhr.responseJSON?.message || 'Failed to save category');
            }
        });
    });

    // Delete category using universalDelete
    $('#categoryTable').on('click', '.delete-product-categories', function() {
        var id = $(this).data('id');
        universalDelete({
            id: id,
            url: '/product-categories/' + id,
            itemName: 'category',
            table: table
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\arclok_admin\resources\views/ProductCategory/index.blade.php ENDPATH**/ ?>