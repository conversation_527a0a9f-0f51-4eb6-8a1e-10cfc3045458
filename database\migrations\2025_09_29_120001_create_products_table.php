<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->decimal('amount', 10, 2);
            $table->foreignId('category_id')->nullable()->constrained('product_categories')->onDelete('set null');
            $table->string('sku')->unique();
            $table->text('short_description')->nullable();
            $table->string('status')->default('active');
            $table->json('images')->nullable();
            $table->decimal('weight', 8, 2)->nullable();
            $table->string('dimensions')->nullable();
            $table->integer('stock_quantity')->default(0);
            $table->decimal('cost_price', 10, 2)->nullable();
            $table->boolean('track_inventory')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->json('meta_data')->nullable();
            $table->softDeletes();
            $table->timestamps();

            // Indexes for performance
            $table->index('status');
            $table->index('category_id');
            $table->index('is_featured');
            $table->index('created_at');
            $table->index(['status', 'created_at']);
            $table->index(['category_id', 'status']);
        });

        // Add full-text index only for supported databases
        $driver = config('database.default');
        $connection = config("database.connections.{$driver}");

        if (in_array($connection['driver'], ['mysql', 'pgsql'])) {
            Schema::table('products', function (Blueprint $table) {
                $table->fullText(['name', 'description']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
