<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(3, true);
        $amount = $this->faker->randomFloat(2, 10, 1000);
        
        return [
            'name' => ucwords($name),
            'slug' => Str::slug($name),
            'description' => $this->faker->paragraphs(3, true),
            'short_description' => $this->faker->sentence(),
            'sku' => 'PRD-' . strtoupper(Str::random(8)),
            'barcode' => $this->faker->ean13(),
            'category_id' => ProductCategory::factory(),
            'amount' => $amount,
            'compare_price' => $amount * 1.2, // 20% higher than selling price
            'cost_price' => $amount * 0.6, // 40% margin
            'stock_quantity' => $this->faker->numberBetween(0, 100),
            'min_stock_level' => $this->faker->numberBetween(1, 10),
            'track_inventory' => $this->faker->boolean(80), // 80% chance of tracking inventory
            'stock_status' => $this->faker->randomElement(['in_stock', 'out_of_stock', 'low_stock']),
            'weight' => $this->faker->randomFloat(2, 0.1, 50),
            'length' => $this->faker->randomFloat(2, 1, 100),
            'width' => $this->faker->randomFloat(2, 1, 100),
            'height' => $this->faker->randomFloat(2, 1, 100),
            'status' => $this->faker->randomElement(['active', 'inactive', 'draft', 'archived']),
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
            'is_digital' => $this->faker->boolean(10), // 10% chance of being digital
            'requires_shipping' => $this->faker->boolean(90), // 90% require shipping
            'is_taxable' => $this->faker->boolean(95), // 95% are taxable
            'meta_title' => ucwords($name),
            'meta_description' => $this->faker->sentence(),
            'meta_keywords' => implode(', ', $this->faker->words(5)),
            'tags' => $this->faker->words(5),
            'has_variants' => $this->faker->boolean(30), // 30% have variants
            'view_count' => $this->faker->numberBetween(0, 1000),
            'purchase_count' => $this->faker->numberBetween(0, 100),
            'average_rating' => $this->faker->randomFloat(1, 1, 5),
            'review_count' => $this->faker->numberBetween(0, 50),
            'published_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the product is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the product is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the product is out of stock.
     */
    public function outOfStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'stock_quantity' => 0,
            'stock_status' => 'out_of_stock',
        ]);
    }

    /**
     * Indicate that the product is in stock.
     */
    public function inStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'stock_quantity' => $this->faker->numberBetween(10, 100),
            'stock_status' => 'in_stock',
        ]);
    }

    /**
     * Indicate that the product is digital.
     */
    public function digital(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_digital' => true,
            'requires_shipping' => false,
            'weight' => 0,
            'length' => 0,
            'width' => 0,
            'height' => 0,
        ]);
    }
}
