<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;

class CacheHelper
{
    /**
     * Cache durations in seconds
     */
    const CACHE_DURATIONS = [
        'short' => 300,      // 5 minutes
        'medium' => 1800,    // 30 minutes
        'long' => 3600,      // 1 hour
        'daily' => 86400,    // 24 hours
        'weekly' => 604800,  // 7 days
    ];

    /**
     * Cache tags for organized cache management
     */
    const CACHE_TAGS = [
        'views' => 'views',
        'queries' => 'queries',
        'config' => 'config',
        'routes' => 'routes',
        'assets' => 'assets',
        'user_data' => 'user_data',
        'system' => 'system',
    ];

    /**
     * Warm up application cache
     *
     * @return array
     */
    public static function warmUp(): array
    {
        $results = [];

        try {
            // Cache configuration
            $results['config'] = self::cacheConfiguration();

            // Cache routes
            $results['routes'] = self::cacheRoutes();

            // Cache views
            $results['views'] = self::cacheViews();

            // Cache common queries
            $results['queries'] = self::cacheCommonQueries();

            // Cache system data
            $results['system'] = self::cacheSystemData();

            Log::info('Cache warm-up completed', $results);

        } catch (\Exception $e) {
            Log::error('Cache warm-up failed: ' . $e->getMessage());
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Clear all application cache
     *
     * @param array $tags
     * @return bool
     */
    public static function clearAll(array $tags = []): bool
    {
        try {
            if (empty($tags)) {
                // Clear all cache
                Cache::flush();
                Artisan::call('cache:clear');
                Artisan::call('config:clear');
                Artisan::call('route:clear');
                Artisan::call('view:clear');
            } else {
                // Clear specific tags
                foreach ($tags as $tag) {
                    if (isset(self::CACHE_TAGS[$tag])) {
                        Cache::tags(self::CACHE_TAGS[$tag])->flush();
                    }
                }
            }

            Log::info('Cache cleared', ['tags' => $tags]);
            return true;

        } catch (\Exception $e) {
            Log::error('Cache clear failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get cache statistics
     *
     * @return array
     */
    public static function getStats(): array
    {
        $stats = [
            'driver' => config('cache.default'),
            'cache_enabled' => config('cache.default') !== 'array',
            'cache_size' => 0,
            'cache_keys' => 0,
            'hit_ratio' => 0,
        ];

        try {
            $driver = config('cache.default');

            switch ($driver) {
                case 'redis':
                    $stats = array_merge($stats, self::getRedisStats());
                    break;

                case 'file':
                    $stats = array_merge($stats, self::getFileStats());
                    break;

                case 'database':
                    $stats = array_merge($stats, self::getDatabaseStats());
                    break;
            }

        } catch (\Exception $e) {
            Log::error('Failed to get cache stats: ' . $e->getMessage());
            $stats['error'] = $e->getMessage();
        }

        return $stats;
    }

    /**
     * Cache with automatic invalidation
     *
     * @param string $key
     * @param callable $callback
     * @param string $duration
     * @param array $tags
     * @return mixed
     */
    public static function remember(string $key, callable $callback, string $duration = 'medium', array $tags = [])
    {
        $seconds = self::CACHE_DURATIONS[$duration] ?? self::CACHE_DURATIONS['medium'];

        if (!empty($tags)) {
            return Cache::tags($tags)->remember($key, $seconds, $callback);
        }

        return Cache::remember($key, $seconds, $callback);
    }

    /**
     * Cache configuration data
     *
     * @return bool
     */
    private static function cacheConfiguration(): bool
    {
        try {
            if (app()->environment('production')) {
                Artisan::call('config:cache');
            }

            // Cache common config values
            $commonConfigs = [
                'app.name',
                'app.url',
                'database.default',
                'cache.default',
                'session.driver',
            ];

            foreach ($commonConfigs as $config) {
                Cache::tags([self::CACHE_TAGS['config']])->put(
                    "config.{$config}",
                    config($config),
                    self::CACHE_DURATIONS['daily']
                );
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Config caching failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Cache routes
     *
     * @return bool
     */
    private static function cacheRoutes(): bool
    {
        try {
            if (app()->environment('production')) {
                Artisan::call('route:cache');
            }
            return true;
        } catch (\Exception $e) {
            Log::error('Route caching failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Cache views
     *
     * @return bool
     */
    private static function cacheViews(): bool
    {
        try {
            // Pre-compile common views
            $commonViews = [
                'Layouts.app',
                'Layouts.sidebar',
                'Layouts.navbar',
                'Layouts.footer',
                'Components.breadcrumb',
            ];

            foreach ($commonViews as $view) {
                if (View::exists($view)) {
                    View::make($view)->render();
                }
            }

            if (app()->environment('production')) {
                Artisan::call('view:cache');
            }

            return true;
        } catch (\Exception $e) {
            Log::error('View caching failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Cache common queries
     *
     * @return bool
     */
    private static function cacheCommonQueries(): bool
    {
        try {
            // Cache user count
            self::remember('users.count', function () {
                return \App\Models\User::count();
            }, 'long', [self::CACHE_TAGS['queries']]);

            // Cache product categories
            self::remember('product_categories.active', function () {
                return \App\Models\ProductCategory::where('status', 'active')->get();
            }, 'medium', [self::CACHE_TAGS['queries']]);

            return true;
        } catch (\Exception $e) {
            Log::error('Query caching failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Cache system data
     *
     * @return bool
     */
    private static function cacheSystemData(): bool
    {
        try {
            // Cache system info
            self::remember('system.info', function () {
                return [
                    'php_version' => PHP_VERSION,
                    'laravel_version' => app()->version(),
                    'server_time' => now()->toISOString(),
                    'timezone' => config('app.timezone'),
                ];
            }, 'daily', [self::CACHE_TAGS['system']]);

            return true;
        } catch (\Exception $e) {
            Log::error('System data caching failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get Redis cache statistics
     *
     * @return array
     */
    private static function getRedisStats(): array
    {
        try {
            $redis = Cache::getRedis();
            $info = $redis->info();

            return [
                'cache_size' => $info['used_memory_human'] ?? 'Unknown',
                'cache_keys' => $redis->dbsize(),
                'hit_ratio' => isset($info['keyspace_hits'], $info['keyspace_misses'])
                    ? round($info['keyspace_hits'] / ($info['keyspace_hits'] + $info['keyspace_misses']) * 100, 2)
                    : 0,
                'connected_clients' => $info['connected_clients'] ?? 0,
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get file cache statistics
     *
     * @return array
     */
    private static function getFileStats(): array
    {
        try {
            $cachePath = storage_path('framework/cache/data');
            $size = 0;
            $keys = 0;

            if (File::exists($cachePath)) {
                $files = File::allFiles($cachePath);
                $keys = count($files);

                foreach ($files as $file) {
                    $size += $file->getSize();
                }
            }

            return [
                'cache_size' => self::formatBytes($size),
                'cache_keys' => $keys,
                'cache_path' => $cachePath,
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get database cache statistics
     *
     * @return array
     */
    private static function getDatabaseStats(): array
    {
        try {
            $table = config('cache.stores.database.table', 'cache');
            $count = DB::table($table)->count();
            $size = DB::table($table)->sum(DB::raw('LENGTH(value)'));

            return [
                'cache_keys' => $count,
                'cache_size' => self::formatBytes($size),
                'table' => $table,
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Format bytes to human readable format
     *
     * @param int $bytes
     * @return string
     */
    private static function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Schedule cache warming
     *
     * @return void
     */
    public static function scheduleWarmUp(): void
    {
        // This would typically be called from a scheduled job
        self::warmUp();
    }

    /**
     * Invalidate cache by pattern
     *
     * @param string $pattern
     * @return bool
     */
    public static function invalidatePattern(string $pattern): bool
    {
        try {
            $driver = config('cache.default');

            if ($driver === 'redis') {
                $redis = Cache::getRedis();
                $keys = $redis->keys("*{$pattern}*");
                if (!empty($keys)) {
                    $redis->del($keys);
                }
            } else {
                // For other drivers, we'll need to flush all cache
                Cache::flush();
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Cache invalidation failed: ' . $e->getMessage());
            return false;
        }
    }
}
