<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Helpers\PerformanceMonitor;

class PerformanceOptimization
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Start performance monitoring
        $startMetrics = PerformanceMonitor::startRequest();

        $response = $next($request);

        // End performance monitoring
        $endMetrics = PerformanceMonitor::endRequest($startMetrics);

        // Only apply optimizations to HTML responses
        if ($this->shouldOptimize($response)) {
            $this->addCacheHeaders($response);
            $this->addSecurityHeaders($response);
            $this->addPerformanceHeaders($response, $endMetrics);
            $this->compressResponse($response);
        }

        return $response;
    }

    /**
     * Check if response should be optimized
     */
    private function shouldOptimize(Response $response): bool
    {
        $contentType = $response->headers->get('Content-Type', '');
        return str_contains($contentType, 'text/html') || empty($contentType);
    }

    /**
     * Add cache headers for better performance
     */
    private function addCacheHeaders(Response $response): void
    {
        if (app()->environment('production')) {
            // Cache static assets for 1 year
            if ($this->isStaticAsset()) {
                $response->headers->set('Cache-Control', 'public, max-age=31536000, immutable');
                $response->headers->set('Expires', gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
            } else {
                // Cache HTML pages for 5 minutes
                $response->headers->set('Cache-Control', 'public, max-age=300, must-revalidate');
            }
        } else {
            // No cache in development
            $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate');
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', '0');
        }

        // Add ETag for better caching
        $etag = md5($response->getContent());
        $response->headers->set('ETag', '"' . $etag . '"');
    }

    /**
     * Add security headers
     */
    private function addSecurityHeaders(Response $response): void
    {
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        if (app()->environment('production')) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        }
    }

    /**
     * Add performance headers
     */
    private function addPerformanceHeaders(Response $response, array $metrics = []): void
    {
        // DNS prefetch for external domains
        $dnsPrefetch = [
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://cdnjs.cloudflare.com',
            'https://cdn.datatables.net',
            'https://cdn.jsdelivr.net'
        ];

        $linkHeader = [];
        foreach ($dnsPrefetch as $domain) {
            $linkHeader[] = '<' . $domain . '>; rel=dns-prefetch';
        }

        if (!empty($linkHeader)) {
            $response->headers->set('Link', implode(', ', $linkHeader));
        }

        // Add performance metrics headers
        if (!empty($metrics)) {
            $response->headers->set('X-Response-Time', round($metrics['response_time'], 2) . 'ms');
            $response->headers->set('X-Memory-Peak', round($metrics['peak_memory'] / 1024 / 1024, 2) . 'MB');
            $response->headers->set('X-Query-Count', $metrics['queries_count']);

            // Add cache performance headers
            if (isset($metrics['cache_hits'], $metrics['cache_misses'])) {
                $total = $metrics['cache_hits'] + $metrics['cache_misses'];
                $hitRatio = $total > 0 ? round(($metrics['cache_hits'] / $total) * 100, 1) : 0;
                $response->headers->set('X-Cache-Hit-Ratio', $hitRatio . '%');
            }
        } else {
            // Fallback to original timing
            if (defined('LARAVEL_START')) {
                $executionTime = microtime(true) - LARAVEL_START;
                $response->headers->set('X-Response-Time', round($executionTime * 1000, 2) . 'ms');
            }
        }

        // Add memory usage header for debugging
        if (app()->environment(['local', 'staging'])) {
            $response->headers->set('X-Memory-Usage', round(memory_get_peak_usage(true) / 1024 / 1024, 2) . 'MB');
            $response->headers->set('X-Memory-Limit', ini_get('memory_limit'));
        }
    }

    /**
     * Compress response content
     */
    private function compressResponse(Response $response): void
    {
        if (app()->environment('production') && function_exists('gzencode')) {
            $content = $response->getContent();

            // Only compress if content is large enough
            if (strlen($content) > 1024) {
                $acceptEncoding = request()->header('Accept-Encoding', '');

                if (str_contains($acceptEncoding, 'gzip')) {
                    $compressed = gzencode($content, 6);
                    if ($compressed !== false) {
                        $response->setContent($compressed);
                        $response->headers->set('Content-Encoding', 'gzip');
                        $response->headers->set('Content-Length', strlen($compressed));
                    }
                }
            }
        }
    }

    /**
     * Check if current request is for a static asset
     */
    private function isStaticAsset(): bool
    {
        $path = request()->path();
        $staticExtensions = ['css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'svg', 'ico', 'woff', 'woff2', 'ttf', 'eot'];

        foreach ($staticExtensions as $ext) {
            if (str_ends_with($path, '.' . $ext)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Minify HTML content
     */
    private function minifyHtml(string $html): string
    {
        if (!app()->environment('production')) {
            return $html;
        }

        // Remove HTML comments (but keep IE conditionals)
        $html = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $html);

        // Remove unnecessary whitespace
        $html = preg_replace('/\s+/', ' ', $html);
        $html = preg_replace('/>\s+</', '><', $html);

        // Remove whitespace around block elements
        $blockElements = ['div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'section', 'article', 'header', 'footer', 'nav', 'aside'];
        foreach ($blockElements as $element) {
            $html = preg_replace('/\s*<' . $element . '([^>]*)>\s*/', '<' . $element . '$1>', $html);
            $html = preg_replace('/\s*<\/' . $element . '>\s*/', '</' . $element . '>', $html);
        }

        return trim($html);
    }

    /**
     * Add resource hints to HTML
     */
    private function addResourceHints(string $html): string
    {
        $hints = [
            '<link rel="preconnect" href="https://fonts.googleapis.com">',
            '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>',
            '<link rel="preconnect" href="https://cdnjs.cloudflare.com">',
            '<link rel="dns-prefetch" href="https://cdn.datatables.net">',
            '<link rel="dns-prefetch" href="https://cdn.jsdelivr.net">'
        ];

        $hintsHtml = implode("\n    ", $hints);

        // Insert hints after <head> tag
        $html = preg_replace('/(<head[^>]*>)/', '$1' . "\n    " . $hintsHtml, $html, 1);

        return $html;
    }

    /**
     * Optimize images in HTML
     */
    private function optimizeImages(string $html): string
    {
        // Add loading="lazy" to images below the fold
        $html = preg_replace_callback('/<img([^>]+)>/i', function ($matches) {
            $imgTag = $matches[0];

            // Skip if already has loading attribute
            if (str_contains($imgTag, 'loading=')) {
                return $imgTag;
            }

            // Add lazy loading
            return str_replace('<img', '<img loading="lazy"', $imgTag);
        }, $html);

        return $html;
    }

    /**
     * Add service worker registration
     */
    private function addServiceWorker(string $html): string
    {
        if (!app()->environment('production')) {
            return $html;
        }

        $serviceWorkerScript = "
        <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
        </script>";

        // Insert before closing body tag
        $html = str_replace('</body>', $serviceWorkerScript . "\n</body>", $html);

        return $html;
    }

    /**
     * Process the response content with all optimizations
     */
    public function processContent(Response $response): void
    {
        $content = $response->getContent();

        if ($this->shouldOptimize($response)) {
            $content = $this->addResourceHints($content);
            $content = $this->optimizeImages($content);
            $content = $this->addServiceWorker($content);
            $content = $this->minifyHtml($content);

            $response->setContent($content);
        }
    }
}
