@php
use App\Helpers\SidebarHelper;
$breadcrumbs = SidebarHelper::getBreadcrumbs();
@endphp

<div class="toolbar py-3 py-lg-6" id="kt_toolbar">
    <div class="container-fluid d-flex flex-stack">
        {{-- Page Title --}}
        <div class="page-title d-flex align-items-center flex-wrap me-3 mb-5 mb-lg-0">
            {{-- Breadcrumb --}}
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    @foreach($breadcrumbs as $index => $breadcrumb)
                        <li class="breadcrumb-item {{ $breadcrumb['active'] ? 'active' : '' }}">
                            @if($breadcrumb['active'] || !$breadcrumb['url'])
                                <span class="text-muted">{{ $breadcrumb['title'] }}</span>
                            @else
                                <a href="{{ $breadcrumb['url'] }}" class="text-muted text-hover-primary">
                                    {{ $breadcrumb['title'] }}
                                </a>
                            @endif
                        </li>
                        @if(!$breadcrumb['active'] && !$loop->last)
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-400 w-5px h-2px"></span>
                            </li>
                        @endif
                    @endforeach
                </ol>
            </nav>
            
            {{-- Page Title --}}
            <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                {{ $pageTitle ?? ucwords(str_replace(['-', '_'], ' ', last(request()->segments()) ?? 'Dashboard')) }}
                @if(isset($pageSubtitle))
                    <span class="page-desc text-muted fs-7 fw-semibold pt-1">{{ $pageSubtitle }}</span>
                @endif
            </h1>
        </div>
        
        {{-- Actions --}}
        @if(isset($pageActions))
            <div class="d-flex align-items-center gap-2 gap-lg-3">
                {!! $pageActions !!}
            </div>
        @endif
    </div>
</div>

{{-- Enhanced Breadcrumb Styles --}}
<style>
.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item + .breadcrumb-item::before {
    display: none;
}

.breadcrumb-item.active span {
    color: #5e6278 !important;
    font-weight: 600;
}

.breadcrumb-item a {
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #009ef7 !important;
}

.page-heading {
    line-height: 1.2;
}

.page-desc {
    margin-top: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .page-title {
        margin-bottom: 1rem !important;
    }
    
    .breadcrumb {
        font-size: 0.75rem;
    }
    
    .page-heading {
        font-size: 1.5rem !important;
    }
}

/* Animation for breadcrumb items */
.breadcrumb-item {
    opacity: 0;
    transform: translateX(-10px);
    animation: breadcrumbFadeIn 0.3s ease forwards;
}

.breadcrumb-item:nth-child(1) { animation-delay: 0.1s; }
.breadcrumb-item:nth-child(2) { animation-delay: 0.2s; }
.breadcrumb-item:nth-child(3) { animation-delay: 0.3s; }
.breadcrumb-item:nth-child(4) { animation-delay: 0.4s; }
.breadcrumb-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes breadcrumbFadeIn {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Print styles */
@media print {
    .toolbar {
        border-bottom: 1px solid #000;
        padding-bottom: 1rem;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item a {
        color: #000 !important;
        text-decoration: underline;
    }
}
</style>
