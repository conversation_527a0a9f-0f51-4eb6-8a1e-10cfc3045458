<div id="kt_app_header" class="app-header">
    <div class="app-container container-fluid d-flex align-items-stretch justify-content-between" id="kt_app_header_container">
        <div class="d-flex align-items-center d-lg-none ms-n3 me-1 me-md-2" title="Show sidebar menu">
            <div class="btn btn-icon btn-active-color-primary w-35px h-35px" id="kt_app_sidebar_mobile_toggle">
                <span class="svg-icon svg-icon-2 svg-icon-md-1">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z" fill="currentColor" />
                        <path opacity="0.3" d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z" fill="currentColor" />
                    </svg>
                </span>
            </div>
        </div>

        <div class="d-flex align-items-stretch justify-content-between flex-lg-grow-1" id="kt_app_header_wrapper">
            {{-- Header Menu --}}
            <div class="app-header-menu app-header-mobile-drawer align-items-stretch" data-kt-drawer="true" data-kt-drawer-name="app-header-menu" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px" data-kt-drawer-direction="end"
                data-kt-drawer-toggle="#kt_app_header_menu_toggle" data-kt-swapper="true" data-kt-swapper-mode="{default: 'append', lg: 'prepend'}" data-kt-swapper-parent="{default: '#kt_app_body', lg: '#kt_app_header_wrapper'}">
                {{-- Version Text --}}
                <div class="d-flex align-items-center">
                    <span class="text-muted fs-7 fw-semibold">Version - 1.0</span>
                </div>
            </div>

            {{-- Navbar --}}
            <div class="flex-shrink-0 app-navbar">
                {{-- Notification Icon --}}
                <div class="app-navbar-item ms-1 ms-lg-3">
                    <a id="notificationButton" href="javascript:void(0);" class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-30px h-30px w-md-40px h-md-40px">
                        <i class="fas fa-bell fa-lg opacity-50"></i>
                    </a>
                </div>
                <!-- Notification box -->
                <div id="notificationBox" class="d-none" style="position:absolute; top:60px; right:80px; width:250px; background:#fff; border:1px solid #ddd; padding:15px; border-radius:6px; box-shadow:0px 2px 8px rgba(0,0,0,0.1);">
                    <p>No notifications</p>
                </div>
                {{-- Notification Icon --}}
                {{-- User menu --}}
                <div class="app-navbar-item ms-1 ms-lg-3" id="kt_header_user_menu_toggle">
                    <div class="cursor-pointer symbol symbol-35px symbol-md-40px" data-kt-menu-trigger="click" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
                        <img src="{{ asset('media/logos/logo_black.png') }}" alt="user" />
                    </div>
                    <div class="py-4 menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg menu-state-color fw-semibold fs-6 w-275px" data-kt-menu="true">
                        <div class="px-3 menu-item">
                            <div class="px-3 menu-content d-flex align-items-center">
                                <div class="symbol symbol-50px me-5">
                                    <img alt="Logo" src="{{ asset('media/logos/logo_black.png') }}" />
                                </div>
                                <div class="d-flex flex-column">
                                    <div class="fw-bold d-flex align-items-center fs-5">Admin User</div>
                                    <a href="#" class="fw-semibold text-muted text-hover-primary fs-7"><EMAIL></a>
                                </div>
                            </div>
                        </div>
                        <div class="separator my-2"></div>
                        <div class="px-5 menu-item">
                            <a href="{{ route('logout') }}" class="px-5 menu-link"><span class="menu-text">Sign Out</span></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>