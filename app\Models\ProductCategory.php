<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductCategory extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'category',
        'status',
    ];

    /**
     * Get the products for the category
     */
    public function products()
    {
        return $this->hasMany(Product::class, 'category_id');
    }
}
