<?php

namespace App\Helpers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Auth;

class PerformanceMonitor
{
    /**
     * Performance thresholds
     */
    const THRESHOLDS = [
        'slow_query' => 1000,      // 1 second in milliseconds
        'memory_limit' => 128,     // 128 MB
        'response_time' => 2000,   // 2 seconds in milliseconds
        'cpu_usage' => 80,         // 80%
    ];

    /**
     * Start performance monitoring for a request
     *
     * @return array
     */
    public static function startRequest(): array
    {
        return [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'start_peak_memory' => memory_get_peak_usage(true),
            'queries_count' => 0,
            'cache_hits' => 0,
            'cache_misses' => 0,
        ];
    }

    /**
     * End performance monitoring and record metrics
     *
     * @param array $startMetrics
     * @return array
     */
    public static function endRequest(array $startMetrics): array
    {
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);

        $metrics = [
            'response_time' => ($endTime - $startMetrics['start_time']) * 1000, // Convert to milliseconds
            'memory_used' => $endMemory - $startMetrics['start_memory'],
            'peak_memory' => $peakMemory,
            'queries_count' => self::getQueryCount(),
            'route' => request()->route()?->getName(),
            'method' => request()->method(),
            'url' => request()->url(),
            'user_id' => Auth::id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ];

        // Record metrics if they exceed thresholds
        self::recordMetrics($metrics);

        // Log slow requests
        if ($metrics['response_time'] > self::THRESHOLDS['response_time']) {
            Log::warning('Slow request detected', $metrics);
        }

        // Log high memory usage
        if ($metrics['peak_memory'] > self::THRESHOLDS['memory_limit'] * 1024 * 1024) {
            Log::warning('High memory usage detected', $metrics);
        }

        return $metrics;
    }

    /**
     * Monitor database query performance
     *
     * @param string $query
     * @param array $bindings
     * @param float $time
     * @return void
     */
    public static function monitorQuery(string $query, array $bindings, float $time): void
    {
        $timeMs = $time * 1000; // Convert to milliseconds

        if ($timeMs > self::THRESHOLDS['slow_query']) {
            Log::warning('Slow query detected', [
                'query' => $query,
                'bindings' => $bindings,
                'time' => $timeMs . 'ms',
                'route' => request()->route()?->getName(),
            ]);

            // Record slow query metric
            self::recordMetric('slow_query', $timeMs, [
                'query' => substr($query, 0, 255), // Truncate for storage
                'route' => request()->route()?->getName(),
            ]);
        }
    }

    /**
     * Monitor memory usage
     *
     * @param string $checkpoint
     * @return array
     */
    public static function checkMemory(string $checkpoint = 'default'): array
    {
        $current = memory_get_usage(true);
        $peak = memory_get_peak_usage(true);

        $metrics = [
            'checkpoint' => $checkpoint,
            'current_memory' => $current,
            'peak_memory' => $peak,
            'formatted_current' => self::formatBytes($current),
            'formatted_peak' => self::formatBytes($peak),
        ];

        // Log high memory usage
        if ($current > self::THRESHOLDS['memory_limit'] * 1024 * 1024) {
            Log::warning("High memory usage at checkpoint: {$checkpoint}", $metrics);
        }

        return $metrics;
    }

    /**
     * Monitor cache performance
     *
     * @param string $operation
     * @param string $key
     * @param bool $hit
     * @return void
     */
    public static function monitorCache(string $operation, string $key, bool $hit = true): void
    {
        $metric = $hit ? 'cache_hit' : 'cache_miss';

        self::recordMetric($metric, 1, [
            'operation' => $operation,
            'key' => substr($key, 0, 255), // Truncate for storage
            'route' => request()->route()?->getName(),
        ]);
    }

    /**
     * Get system performance metrics
     *
     * @return array
     */
    public static function getSystemMetrics(): array
    {
        $metrics = [
            'timestamp' => now()->toISOString(),
            'memory' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => ini_get('memory_limit'),
                'formatted' => [
                    'current' => self::formatBytes(memory_get_usage(true)),
                    'peak' => self::formatBytes(memory_get_peak_usage(true)),
                ],
            ],
            'php' => [
                'version' => PHP_VERSION,
                'opcache_enabled' => function_exists('opcache_get_status') && opcache_get_status(),
                'max_execution_time' => ini_get('max_execution_time'),
            ],
            'database' => self::getDatabaseMetrics(),
            'cache' => self::getCacheMetrics(),
        ];

        // Add server metrics if available
        if (function_exists('sys_getloadavg')) {
            $metrics['server'] = [
                'load_average' => sys_getloadavg(),
                'cpu_count' => self::getCpuCount(),
            ];
        }

        return $metrics;
    }

    /**
     * Get performance analytics
     *
     * @param int $hours
     * @return array
     */
    public static function getAnalytics(int $hours = 24): array
    {
        $since = now()->subHours($hours);

        try {
            $analytics = [
                'timeframe' => "{$hours} hours",
                'slow_queries' => DB::table('performance_metrics')
                    ->where('metric_type', 'slow_query')
                    ->where('recorded_at', '>=', $since)
                    ->count(),
                'avg_response_time' => DB::table('performance_metrics')
                    ->where('metric_type', 'response_time')
                    ->where('recorded_at', '>=', $since)
                    ->avg('value'),
                'peak_memory' => DB::table('performance_metrics')
                    ->where('metric_type', 'memory_usage')
                    ->where('recorded_at', '>=', $since)
                    ->max('value'),
                'cache_hit_ratio' => self::getCacheHitRatio($since),
                'top_slow_routes' => self::getTopSlowRoutes($since),
            ];

            return $analytics;
        } catch (\Exception $e) {
            Log::error('Failed to get performance analytics: ' . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Record a performance metric
     *
     * @param string $type
     * @param float $value
     * @param array $metadata
     * @return void
     */
    private static function recordMetric(string $type, float $value, array $metadata = []): void
    {
        try {
            // Check if performance_metrics table exists
            if (Schema::hasTable('performance_metrics')) {
                DB::table('performance_metrics')->insert([
                    'metric_name' => $metadata['route'] ?? 'unknown',
                    'metric_type' => $type,
                    'value' => $value,
                    'route' => $metadata['route'] ?? null,
                    'method' => request()->method(),
                    'metadata' => json_encode($metadata),
                    'recorded_at' => now(),
                ]);
            }
        } catch (\Exception $e) {
            // Silently fail to avoid breaking the application
            Log::debug('Failed to record performance metric: ' . $e->getMessage());
        }
    }

    /**
     * Record multiple metrics
     *
     * @param array $metrics
     * @return void
     */
    private static function recordMetrics(array $metrics): void
    {
        foreach ($metrics as $type => $value) {
            if (is_numeric($value)) {
                self::recordMetric($type, $value, $metrics);
            }
        }
    }

    /**
     * Get database query count
     *
     * @return int
     */
    private static function getQueryCount(): int
    {
        return count(DB::getQueryLog());
    }

    /**
     * Get database performance metrics
     *
     * @return array
     */
    private static function getDatabaseMetrics(): array
    {
        try {
            return [
                'query_count' => self::getQueryCount(),
                'connection_name' => config('database.default'),
                'driver' => config('database.connections.' . config('database.default') . '.driver'),
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get cache performance metrics
     *
     * @return array
     */
    private static function getCacheMetrics(): array
    {
        try {
            return CacheHelper::getStats();
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get cache hit ratio
     *
     * @param \Carbon\Carbon $since
     * @return float
     */
    private static function getCacheHitRatio($since): float
    {
        try {
            $hits = DB::table('performance_metrics')
                ->where('metric_type', 'cache_hit')
                ->where('recorded_at', '>=', $since)
                ->sum('value');

            $misses = DB::table('performance_metrics')
                ->where('metric_type', 'cache_miss')
                ->where('recorded_at', '>=', $since)
                ->sum('value');

            $total = $hits + $misses;
            return $total > 0 ? round(($hits / $total) * 100, 2) : 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get top slow routes
     *
     * @param \Carbon\Carbon $since
     * @return array
     */
    private static function getTopSlowRoutes($since): array
    {
        try {
            return DB::table('performance_metrics')
                ->select('route', DB::raw('AVG(value) as avg_time'), DB::raw('COUNT(*) as count'))
                ->where('metric_type', 'response_time')
                ->where('recorded_at', '>=', $since)
                ->whereNotNull('route')
                ->groupBy('route')
                ->orderBy('avg_time', 'desc')
                ->limit(10)
                ->get()
                ->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get CPU count
     *
     * @return int
     */
    private static function getCpuCount(): int
    {
        if (PHP_OS_FAMILY === 'Windows') {
            return (int) shell_exec('echo %NUMBER_OF_PROCESSORS%');
        } else {
            return (int) shell_exec('nproc');
        }
    }

    /**
     * Format bytes to human readable format
     *
     * @param int $bytes
     * @return string
     */
    private static function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Clean up old performance metrics
     *
     * @param int $days
     * @return int
     */
    public static function cleanup(int $days = 30): int
    {
        try {
            $cutoff = now()->subDays($days);
            return DB::table('performance_metrics')
                ->where('recorded_at', '<', $cutoff)
                ->delete();
        } catch (\Exception $e) {
            Log::error('Failed to cleanup performance metrics: ' . $e->getMessage());
            return 0;
        }
    }
}
