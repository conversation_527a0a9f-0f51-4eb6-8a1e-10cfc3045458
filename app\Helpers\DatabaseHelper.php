<?php

namespace App\Helpers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class DatabaseHelper
{
    /**
     * Cache duration in seconds (1 hour default)
     */
    const DEFAULT_CACHE_DURATION = 3600;

    /**
     * Execute a cached query
     *
     * @param string $cacheKey
     * @param callable $query
     * @param int $duration
     * @return mixed
     */
    public static function cachedQuery(string $cacheKey, callable $query, int $duration = self::DEFAULT_CACHE_DURATION)
    {
        return Cache::remember($cacheKey, $duration, $query);
    }

    /**
     * Execute a query with performance monitoring
     *
     * @param callable $query
     * @param string $queryName
     * @return mixed
     */
    public static function monitoredQuery(callable $query, string $queryName = 'unknown')
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        try {
            $result = $query();
            
            $executionTime = (microtime(true) - $startTime) * 1000; // Convert to milliseconds
            $memoryUsed = memory_get_usage(true) - $startMemory;
            
            // Log slow queries
            if ($executionTime > 1000) { // Queries taking more than 1 second
                Log::warning("Slow query detected: {$queryName}", [
                    'execution_time' => $executionTime . 'ms',
                    'memory_used' => self::formatBytes($memoryUsed),
                    'query_name' => $queryName
                ]);
            }

            // Store performance metrics
            self::recordPerformanceMetric('query_time', $executionTime, $queryName);
            self::recordPerformanceMetric('memory_usage', $memoryUsed, $queryName);

            return $result;
        } catch (\Exception $e) {
            Log::error("Query failed: {$queryName}", [
                'error' => $e->getMessage(),
                'execution_time' => (microtime(true) - $startTime) * 1000 . 'ms'
            ]);
            throw $e;
        }
    }

    /**
     * Optimize DataTables query for better performance
     *
     * @param Builder $query
     * @param array $columns
     * @param array $searchableColumns
     * @return Builder
     */
    public static function optimizeDataTablesQuery(Builder $query, array $columns = [], array $searchableColumns = [])
    {
        // Only select necessary columns
        if (!empty($columns)) {
            $query->select($columns);
        }

        // Add indexes hint for MySQL
        if (config('database.default') === 'mysql') {
            $table = $query->getModel()->getTable();
            $query->from(DB::raw("{$table} USE INDEX (PRIMARY)"));
        }

        // Optimize search functionality
        if (!empty($searchableColumns) && request()->has('search.value')) {
            $searchValue = request('search.value');
            if (!empty($searchValue)) {
                $query->where(function ($q) use ($searchableColumns, $searchValue) {
                    foreach ($searchableColumns as $column) {
                        $q->orWhere($column, 'LIKE', "%{$searchValue}%");
                    }
                });
            }
        }

        // Optimize ordering
        if (request()->has('order')) {
            $orderColumn = request('order.0.column');
            $orderDir = request('order.0.dir', 'asc');
            
            if (isset($columns[$orderColumn])) {
                $query->orderBy($columns[$orderColumn], $orderDir);
            }
        }

        return $query;
    }

    /**
     * Batch insert with better performance
     *
     * @param string $table
     * @param array $data
     * @param int $chunkSize
     * @return bool
     */
    public static function batchInsert(string $table, array $data, int $chunkSize = 1000): bool
    {
        if (empty($data)) {
            return true;
        }

        $chunks = array_chunk($data, $chunkSize);
        
        DB::transaction(function () use ($table, $chunks) {
            foreach ($chunks as $chunk) {
                DB::table($table)->insert($chunk);
            }
        });

        return true;
    }

    /**
     * Optimize table with database-specific commands
     *
     * @param string $table
     * @return bool
     */
    public static function optimizeTable(string $table): bool
    {
        try {
            $driver = config('database.default');
            $connection = config("database.connections.{$driver}");

            switch ($connection['driver']) {
                case 'mysql':
                    DB::statement("OPTIMIZE TABLE {$table}");
                    DB::statement("ANALYZE TABLE {$table}");
                    break;
                    
                case 'pgsql':
                    DB::statement("VACUUM ANALYZE {$table}");
                    break;
                    
                case 'sqlite':
                    DB::statement("VACUUM");
                    break;
            }

            return true;
        } catch (\Exception $e) {
            Log::error("Failed to optimize table {$table}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get database performance statistics
     *
     * @return array
     */
    public static function getPerformanceStats(): array
    {
        $stats = [];

        try {
            $driver = config('database.default');
            $connection = config("database.connections.{$driver}");

            switch ($connection['driver']) {
                case 'mysql':
                    $stats = self::getMySQLStats();
                    break;
                    
                case 'pgsql':
                    $stats = self::getPostgreSQLStats();
                    break;
                    
                default:
                    $stats = self::getGenericStats();
            }
        } catch (\Exception $e) {
            Log::error("Failed to get performance stats: " . $e->getMessage());
        }

        return $stats;
    }

    /**
     * Clear query cache
     *
     * @param string|null $pattern
     * @return bool
     */
    public static function clearQueryCache(string $pattern = null): bool
    {
        try {
            if ($pattern) {
                // Clear specific cache pattern
                $keys = Cache::getRedis()->keys("*{$pattern}*");
                if (!empty($keys)) {
                    Cache::getRedis()->del($keys);
                }
            } else {
                // Clear all query cache
                Cache::flush();
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to clear query cache: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Record performance metric
     *
     * @param string $metricType
     * @param float $value
     * @param string $context
     * @return void
     */
    private static function recordPerformanceMetric(string $metricType, float $value, string $context = ''): void
    {
        try {
            DB::table('performance_metrics')->insert([
                'metric_name' => $context,
                'metric_type' => $metricType,
                'value' => $value,
                'route' => request()->route()?->getName(),
                'method' => request()->method(),
                'metadata' => json_encode([
                    'user_id' => auth()->id(),
                    'ip_address' => request()->ip(),
                    'user_agent' => request()->userAgent()
                ]),
                'recorded_at' => now()
            ]);
        } catch (\Exception $e) {
            // Silently fail to avoid breaking the application
            Log::debug("Failed to record performance metric: " . $e->getMessage());
        }
    }

    /**
     * Get MySQL-specific performance stats
     *
     * @return array
     */
    private static function getMySQLStats(): array
    {
        $stats = [];
        
        try {
            // Query cache stats
            $cacheStats = DB::select("SHOW STATUS LIKE 'Qcache%'");
            foreach ($cacheStats as $stat) {
                $stats['query_cache'][$stat->Variable_name] = $stat->Value;
            }

            // Connection stats
            $connectionStats = DB::select("SHOW STATUS LIKE 'Connections'");
            $stats['connections'] = $connectionStats[0]->Value ?? 0;

            // Slow query stats
            $slowQueries = DB::select("SHOW STATUS LIKE 'Slow_queries'");
            $stats['slow_queries'] = $slowQueries[0]->Value ?? 0;

        } catch (\Exception $e) {
            Log::error("Failed to get MySQL stats: " . $e->getMessage());
        }

        return $stats;
    }

    /**
     * Get PostgreSQL-specific performance stats
     *
     * @return array
     */
    private static function getPostgreSQLStats(): array
    {
        $stats = [];
        
        try {
            // Database stats
            $dbStats = DB::select("SELECT * FROM pg_stat_database WHERE datname = ?", [config('database.connections.pgsql.database')]);
            if (!empty($dbStats)) {
                $stats['database'] = (array) $dbStats[0];
            }

        } catch (\Exception $e) {
            Log::error("Failed to get PostgreSQL stats: " . $e->getMessage());
        }

        return $stats;
    }

    /**
     * Get generic database stats
     *
     * @return array
     */
    private static function getGenericStats(): array
    {
        return [
            'driver' => config('database.default'),
            'cache_enabled' => config('cache.default') !== 'array',
            'query_log_enabled' => config('database.connections.' . config('database.default') . '.options.log_queries', false)
        ];
    }

    /**
     * Format bytes to human readable format
     *
     * @param int $bytes
     * @return string
     */
    private static function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get optimized pagination for large datasets
     *
     * @param Builder $query
     * @param int $perPage
     * @param string $cursorColumn
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public static function optimizedPagination(Builder $query, int $perPage = 15, string $cursorColumn = 'id')
    {
        // Use cursor-based pagination for better performance on large datasets
        if (request()->has('cursor')) {
            $cursor = request('cursor');
            $query->where($cursorColumn, '>', $cursor);
        }

        return $query->orderBy($cursorColumn)->paginate($perPage);
    }
}
