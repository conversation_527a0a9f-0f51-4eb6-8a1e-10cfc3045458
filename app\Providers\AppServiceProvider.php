<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use App\Helpers\SidebarHelper;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register SidebarHelper as singleton
        $this->app->singleton('sidebar', function () {
            return new SidebarHelper();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register Blade directives for sidebar functionality
        Blade::directive('sidebarActive', function ($expression) {
            return "<?php echo App\Helpers\SidebarHelper::getActiveClass($expression); ?>";
        });

        Blade::directive('sidebarExpanded', function ($expression) {
            return "<?php echo App\Helpers\SidebarHelper::getExpandedClass($expression); ?>";
        });

        Blade::directive('sidebarShouldExpand', function ($expression) {
            return "<?php echo App\Helpers\SidebarHelper::shouldExpand($expression) ? 'true' : 'false'; ?>";
        });

        // Performance optimizations
        if ($this->app->environment('production')) {
            // Enable view caching in production
            config(['view.cache' => true]);

            // Optimize cache configuration
            config([
                'cache.default' => env('CACHE_DRIVER', 'redis'),
                'session.driver' => env('SESSION_DRIVER', 'redis'),
                'queue.default' => env('QUEUE_CONNECTION', 'redis'),
            ]);

            // Enable OPcache optimizations
            if (function_exists('opcache_compile_file')) {
                ini_set('opcache.enable', 1);
                ini_set('opcache.memory_consumption', 256);
                ini_set('opcache.max_accelerated_files', 20000);
                ini_set('opcache.validate_timestamps', 0);
            }
        }

        // Register cache warming on application boot
        $this->app->booted(function () {
            if ($this->app->environment('production') && !$this->app->runningInConsole()) {
                // Warm up cache in background
                \App\Helpers\CacheHelper::scheduleWarmUp();
            }
        });
    }
}
