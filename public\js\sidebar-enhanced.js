/**
 * Enhanced Sidebar Functionality
 * Handles active states, accordion behavior, and performance optimizations
 */

class SidebarManager {
    constructor() {
        this.sidebar = document.getElementById('kt_app_sidebar');
        this.menuItems = document.querySelectorAll('.menu-item');
        this.accordionItems = document.querySelectorAll('.menu-accordion');
        this.menuLinks = document.querySelectorAll('.menu-link');
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeActiveStates();
        this.setupKeyboardNavigation();
        this.setupPerformanceOptimizations();
        this.handleResponsiveStates();
    }

    setupEventListeners() {
        // Handle accordion clicks with improved performance
        this.accordionItems.forEach(item => {
            const trigger = item.querySelector('[data-kt-menu-trigger="click"]');
            if (trigger) {
                trigger.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.toggleAccordion(item);
                });
            }
        });

        // Handle menu link clicks for analytics and smooth transitions
        this.menuLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                this.handleMenuClick(e, link);
            });
        });

        // Handle sidebar toggle
        const sidebarToggle = document.getElementById('kt_app_sidebar_toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.handleSidebarToggle();
            });
        }

        // Handle window resize for responsive behavior
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleResponsiveStates();
            }, 250);
        });
    }

    toggleAccordion(accordionItem) {
        const submenu = accordionItem.querySelector('.menu-sub-accordion');
        const arrow = accordionItem.querySelector('.menu-arrow');
        const isExpanded = accordionItem.classList.contains('show');

        // Close other accordions (optional - remove for multiple open accordions)
        this.accordionItems.forEach(item => {
            if (item !== accordionItem && item.classList.contains('show')) {
                this.closeAccordion(item);
            }
        });

        if (isExpanded) {
            this.closeAccordion(accordionItem);
        } else {
            this.openAccordion(accordionItem);
        }

        // Store accordion state in localStorage
        this.saveAccordionState();
    }

    openAccordion(accordionItem) {
        const submenu = accordionItem.querySelector('.menu-sub-accordion');
        const arrow = accordionItem.querySelector('.menu-arrow');

        accordionItem.classList.add('show');
        if (submenu) {
            submenu.classList.add('show');
            submenu.style.display = 'block';
            
            // Smooth animation
            submenu.style.maxHeight = '0px';
            submenu.style.opacity = '0';
            
            requestAnimationFrame(() => {
                submenu.style.transition = 'max-height 0.3s ease, opacity 0.3s ease';
                submenu.style.maxHeight = submenu.scrollHeight + 'px';
                submenu.style.opacity = '1';
            });
        }

        if (arrow) {
            arrow.style.transform = 'rotate(90deg)';
        }

        // Accessibility
        accordionItem.setAttribute('aria-expanded', 'true');
    }

    closeAccordion(accordionItem) {
        const submenu = accordionItem.querySelector('.menu-sub-accordion');
        const arrow = accordionItem.querySelector('.menu-arrow');

        if (submenu) {
            submenu.style.transition = 'max-height 0.3s ease, opacity 0.3s ease';
            submenu.style.maxHeight = '0px';
            submenu.style.opacity = '0';
            
            setTimeout(() => {
                accordionItem.classList.remove('show');
                submenu.classList.remove('show');
                submenu.style.display = 'none';
                submenu.style.maxHeight = '';
                submenu.style.opacity = '';
                submenu.style.transition = '';
            }, 300);
        } else {
            accordionItem.classList.remove('show');
        }

        if (arrow) {
            arrow.style.transform = 'rotate(0deg)';
        }

        // Accessibility
        accordionItem.setAttribute('aria-expanded', 'false');
    }

    handleMenuClick(event, link) {
        // Add loading state for better UX
        if (!link.getAttribute('href') || link.getAttribute('href') === '#') {
            event.preventDefault();
            return;
        }

        // Add click analytics (optional)
        this.trackMenuClick(link);

        // Add loading indicator for external links
        if (!link.getAttribute('href').startsWith('#')) {
            this.showLoadingState(link);
        }
    }

    showLoadingState(link) {
        const originalText = link.querySelector('.menu-title').textContent;
        const titleElement = link.querySelector('.menu-title');
        
        titleElement.innerHTML = `<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>${originalText}`;
        
        // Remove loading state after navigation or timeout
        setTimeout(() => {
            titleElement.textContent = originalText;
        }, 2000);
    }

    trackMenuClick(link) {
        // Optional: Send analytics data
        const menuTitle = link.querySelector('.menu-title')?.textContent;
        const href = link.getAttribute('href');
        
        console.log('Menu clicked:', { title: menuTitle, href: href });
        
        // Example: Send to analytics service
        // analytics.track('sidebar_menu_click', { menu: menuTitle, url: href });
    }

    initializeActiveStates() {
        // Ensure active states are properly set on page load
        const currentPath = window.location.pathname;
        
        this.menuLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && (href === currentPath || currentPath.includes(href.replace(/^\//, '')))) {
                link.classList.add('active');
                
                // Expand parent accordion if this is a submenu item
                const parentAccordion = link.closest('.menu-accordion');
                if (parentAccordion) {
                    this.openAccordion(parentAccordion);
                    parentAccordion.classList.add('has-active-child');
                }
            }
        });

        // Restore accordion states from localStorage
        this.restoreAccordionState();
    }

    setupKeyboardNavigation() {
        // Add keyboard navigation support
        this.menuLinks.forEach((link, index) => {
            link.setAttribute('tabindex', '0');
            
            link.addEventListener('keydown', (e) => {
                switch (e.key) {
                    case 'Enter':
                    case ' ':
                        e.preventDefault();
                        link.click();
                        break;
                    case 'ArrowDown':
                        e.preventDefault();
                        this.focusNextMenuItem(index);
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        this.focusPreviousMenuItem(index);
                        break;
                    case 'Escape':
                        link.blur();
                        break;
                }
            });
        });
    }

    focusNextMenuItem(currentIndex) {
        const nextIndex = (currentIndex + 1) % this.menuLinks.length;
        this.menuLinks[nextIndex].focus();
    }

    focusPreviousMenuItem(currentIndex) {
        const prevIndex = currentIndex === 0 ? this.menuLinks.length - 1 : currentIndex - 1;
        this.menuLinks[prevIndex].focus();
    }

    handleSidebarToggle() {
        const body = document.body;
        const isMinimized = body.hasAttribute('data-kt-app-sidebar-minimize');
        
        // Add smooth transition
        this.sidebar.style.transition = 'all 0.3s ease';
        
        // Optional: Save sidebar state
        localStorage.setItem('sidebar_minimized', isMinimized ? 'false' : 'true');
    }

    setupPerformanceOptimizations() {
        // Lazy load menu icons
        const menuIcons = this.sidebar.querySelectorAll('.menu-icon img');
        if ('IntersectionObserver' in window) {
            const iconObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            iconObserver.unobserve(img);
                        }
                    }
                });
            });

            menuIcons.forEach(icon => iconObserver.observe(icon));
        }

        // Debounce scroll events
        let scrollTimeout;
        const menuWrapper = document.getElementById('kt_app_sidebar_menu_wrapper');
        if (menuWrapper) {
            menuWrapper.addEventListener('scroll', () => {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(() => {
                    this.handleMenuScroll();
                }, 100);
            });
        }
    }

    handleMenuScroll() {
        // Optional: Handle scroll-based optimizations
        // Example: Hide/show elements based on scroll position
    }

    handleResponsiveStates() {
        const isMobile = window.innerWidth < 768;
        const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;
        
        if (isMobile) {
            // Mobile-specific optimizations
            this.sidebar.classList.add('mobile-sidebar');
        } else {
            this.sidebar.classList.remove('mobile-sidebar');
        }
    }

    saveAccordionState() {
        const accordionStates = {};
        this.accordionItems.forEach((item, index) => {
            accordionStates[index] = item.classList.contains('show');
        });
        localStorage.setItem('sidebar_accordion_states', JSON.stringify(accordionStates));
    }

    restoreAccordionState() {
        const savedStates = localStorage.getItem('sidebar_accordion_states');
        if (savedStates) {
            try {
                const states = JSON.parse(savedStates);
                this.accordionItems.forEach((item, index) => {
                    if (states[index] && !item.classList.contains('has-active-child')) {
                        this.openAccordion(item);
                    }
                });
            } catch (e) {
                console.warn('Failed to restore accordion states:', e);
            }
        }
    }

    // Public methods for external use
    expandAccordion(selector) {
        const accordion = document.querySelector(selector);
        if (accordion) {
            this.openAccordion(accordion);
        }
    }

    collapseAccordion(selector) {
        const accordion = document.querySelector(selector);
        if (accordion) {
            this.closeAccordion(accordion);
        }
    }

    setActiveMenuItem(selector) {
        // Remove all active states
        this.menuLinks.forEach(link => link.classList.remove('active'));
        
        // Set new active state
        const menuItem = document.querySelector(selector);
        if (menuItem) {
            menuItem.classList.add('active');
            
            // Expand parent if needed
            const parentAccordion = menuItem.closest('.menu-accordion');
            if (parentAccordion) {
                this.openAccordion(parentAccordion);
            }
        }
    }
}

// Initialize sidebar manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.sidebarManager = new SidebarManager();
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SidebarManager;
}
